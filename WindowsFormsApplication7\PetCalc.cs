﻿using Shikong.Pokemon2.PCG.占卜屋;
using Shikong.Pokemon2.PCG.时空屋;
using Shikong.Pokemon2.PCG.装备宝石;
using Shikong.Pokemon2.PCG.魂宠;
using ShikongPlus.Pokemon2.PCG.龙珠;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace Shikong.Pokemon2.PCG
{
    public static class PetCalc
    {
        /// <summary>
        /// 称号属性
        /// </summary>
        internal static Dictionary<string, string> Tmp = new Dictionary<string, string>();

        private static double CalcSkill(string 基础, string 等级, string special = "0")
        {
            return Convert.ToDouble(基础) + Convert.ToDouble(等级) * NumEncrypt.零点零零五() + Convert.ToDouble(special);
        }

        private static long CalcEquipment_L(string 值, short 等级, short wxlb)
        {
            if (wxlb == 0)
            {
                if (等级 >= 0 && 等级 <= 10)
                {
                    return Convert.ToInt64(Convert.ToInt64(值) * (NumEncrypt.一() + NumEncrypt.零点零五() * 等级));
                }

                return Convert.ToInt64(Convert.ToInt64(值) * (NumEncrypt.零点五() + NumEncrypt.零点一() * 等级));
            }
            else if (wxlb == 1)
            {
                if (等级 >= 0 && 等级 <= 10)
                {
                    return Convert.ToInt64(Convert.ToInt64(值) * (NumEncrypt.零点八五() + NumEncrypt.零点零五() * 等级));
                }

                return Convert.ToInt64(Convert.ToInt64(值) * (NumEncrypt.零点三五() + NumEncrypt.零点一() * 等级));
            }
            else
            {
                if (等级 >= 0 && 等级 <= 10)
                {
                    return Convert.ToInt64(Convert.ToInt64(值) * (NumEncrypt.一点一五() + NumEncrypt.零点零五() * 等级));
                }
                else
                {
                    return Convert.ToInt64(Convert.ToInt64(值) * (NumEncrypt.零点六五() + NumEncrypt.零点一() * 等级));
                }
            }
        }

        private static double CalcEquipment_D(string 值, short 等级, short wxlb)
        {
            if (wxlb == 0)
            {
                if (等级 >= 0 && 等级 <= 10)
                {
                    return Convert.ToDouble(值) + NumEncrypt.零点零一() * 等级;
                }

                return Convert.ToDouble(值) + NumEncrypt.零点零二() * 等级 - NumEncrypt.零点一();
            }
            else if (wxlb == 1)
            {
                if (等级 >= 0 && 等级 <= 10)
                {
                    return Convert.ToDouble(值) + NumEncrypt.零点零一() * 等级 - NumEncrypt.零点零三();
                }

                return Convert.ToDouble(值) + NumEncrypt.零点零二() * 等级 - NumEncrypt.零点一三();
            }
            else
            {
                if (等级 >= 0 && 等级 <= 10)
                {
                    return Convert.ToDouble(值) + NumEncrypt.零点零一() * 等级 + NumEncrypt.零点零三();
                }

                return Convert.ToDouble(值) + NumEncrypt.零点零二() * 等级 - NumEncrypt.零点零七();
            }
        }

        private static bool CalcOrNot(string 具体类型)
        {
            if (具体类型 != null && 具体类型 != "Null" && 具体类型.Length > 0)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 宠物序号 宠物等级 宠物形象 宠物技能 宠物cc //称号状态和装备信息在别的代码中处理 
        /// </summary>
        internal static string[] PetInfoBuffer = null;

        internal static PetInfo PetInfoCache = null;
        internal static string PetInfoCacheMd5 = null;

        internal static void RenewBuffs()
        {
            PetInfoCache = null;
            PetInfoCacheMd5 = null;
            PetInfoBuffer = null;
            //LogSystem.JoinLog(LogSystem.EventKind.宠物缓存, "清空");
        }

        internal static PetInfo CalcFightPetInfo(PetInfo petInfo)
        {
            if (PetInfoCache == null || PetInfoBuffer == null)
            {
                return CalcPetAttribute(petInfo, true, false, true);
            }

            /*if (PetInfoCacheMd5 != null && PetInfoCache.ToMd5() != PetInfoCacheMd5)
            {
                ///进入到这里肯定是用CE修改过了
                return CalcPetAttribute(petInfo, true, false, true);
            }*/
            else
            {
                if (petInfo.宠物序号.Equals(PetInfoBuffer[0]) && petInfo.等级.Equals(PetInfoBuffer[1]) &&
                    petInfo.形象.Equals(PetInfoBuffer[2]) && petInfo.技能列表.Equals(PetInfoBuffer[3]) &&
                    petInfo.成长.Equals(PetInfoBuffer[4]))
                {
                    //LogSystem.JoinLog(LogSystem.EventKind.宠物缓存,"获取成功");
                    PetInfoCache.魔法 = PetInfoCache.最大魔法;
                    PetInfoCache.生命 = PetInfoCache.最大生命;
                    PetInfoCache.当前经验 = petInfo.当前经验;
                    return PetInfoCache;
                }
                else
                {
                    RenewBuffs();
                    return CalcPetAttribute(petInfo, true, false, true);
                }
            }
        }

        private static string CalcBaseAttribute(PetInfo petInfo, string 属性类型, int 种族值)
        {
            if (petInfo.五行 == "巫")
            {
                return ((long)(1.0 * Convert.ToInt64(种族值) * Convert.ToDouble(petInfo.成长) * (Convert.ToInt64(petInfo.等级) - 1) +
                     Convert.ToInt64(属性类型))).ToString();
            }
                //计算境界属性
            double stateBuffer = 1.0;
            int x = 0;
            foreach (int xh in DataProcess.PetStates.Keys)
            {
                if (DataProcess.PetStates[xh].Equals(petInfo.境界))
                {
                    if (x < 16)
                    {
                        stateBuffer = stateBuffer + Convert.ToDouble(xh) / 100.0;//16级以下，每级增加1%的属性
                    }
                    else if (x>15)
                    {
                        stateBuffer = stateBuffer +0.16+ ((Convert.ToDouble(xh)-15) / 100.0)*3;//16级以上，每级增加3%的属性
                    }
                }
                x++;
            }
            return ((long) (stateBuffer * Convert.ToInt64(种族值) * Convert.ToDouble(petInfo.成长) * (Convert.ToInt64(petInfo.等级) - 1) +
                     Convert.ToInt64(属性类型))).ToString();
        }


        internal static PetInfo CalcPetAttribute(PetInfo petInfo, bool calc, bool gw = false, bool cache = false)
        {

            #region 变量初始化
            PetInfo 宠物 = new PetInfo { 技能列表 = petInfo.技能列表 };
            Double OneNum = 99999999999.0;// Convert.ToDouble(petInfo.最大生命)//宠物属性判断
            double 攻击百分比 = 0.0;
            double 防御百分比 = 0.0;
            double 魔法百分比 = 0.0;
            double 闪避百分比 = 0.0;
            double 生命百分比 = 0.0;
            double 速度百分比 = 0.0;
            double 命中百分比 = 0.0;
            long 攻击增量 = 0;
            long 防御增量 = 0;
            long 魔法增量 = 0;
            long 闪避增量 = 0;
            long 生命增量 = 0;
            long 速度增量 = 0;
            long 命中增量 = 0;
            int 生命种族值;
            int 魔法种族值;
            int 攻击种族值;
            int 闪避种族值;
            int 速度种族值;
            int 命中种族值;
            int 防御种族值;
            if (gw)
            {
                petInfo.成长 =
                    (DataProcess.GetFloat("5AF3988E") * Convert.ToDouble(petInfo.成长)).ToString(CultureInfo
                        .InvariantCulture); //0.82
            }
            #endregion

            #region 种族值计算代码
            if (petInfo.五行.Equals("神"))
            {
                生命种族值 = 33;
                魔法种族值 = 6;
                攻击种族值 = 7;
                防御种族值 = 4;
                闪避种族值 = 4;
                速度种族值 = 15;
                命中种族值 = 7;
            }
            else if (petInfo.五行.Equals("神圣"))
            {
                生命种族值 = 44;
                魔法种族值 = 7;
                攻击种族值 = 8;
                防御种族值 = 4;
                闪避种族值 = 3;
                速度种族值 = 15;
                命中种族值 = 7;
            }
            else if (petInfo.五行.Equals("魔")) //地狱
            {
                生命种族值 = 44;
                魔法种族值 = 7;
                攻击种族值 = 8;
                防御种族值 = 4;
                闪避种族值 = 3;
                速度种族值 = 15;
                命中种族值 = 7;
            }
            /*else if (宠物属性.五行.Equals("妖")) //皮糙肉厚怪
            {
                生命种族值 = 44;
                魔法种族值 = 7;
                攻击种族值 = 5;
                防御种族值 = 6;
                命中种族值 = 4;
                速度种族值 = 15;
                闪避种族值 = 7;


            }*/
            else if (petInfo.五行.Equals("聖") || petInfo.五行.Equals("佛"))
            {
                if (petInfo.宠物名字.Equals("白喵"))
                {
                    生命种族值 = 33;
                    魔法种族值 = 6;
                    攻击种族值 = 7;
                    防御种族值 = 4;
                    闪避种族值 = 4;
                    速度种族值 = 15;
                    命中种族值 = 7;
                }
                else
                {
                    生命种族值 = 50;
                    魔法种族值 = 7;
                    攻击种族值 = 11;
                    防御种族值 = 2;
                    闪避种族值 = 3;
                    速度种族值 = 15;
                    命中种族值 = 6;
                }
            }
            else if (petInfo.五行.Equals("金"))
            {
                生命种族值 = 21;
                魔法种族值 = 6;
                攻击种族值 = 4;
                防御种族值 = 3;
                命中种族值 = 4;
                速度种族值 = 7;
                闪避种族值 = 4;
            }
            else if (petInfo.五行.Equals("木"))
            {
                生命种族值 = 20;
                魔法种族值 = 6;
                攻击种族值 = 4;
                防御种族值 = 3;
                命中种族值 = 5;
                速度种族值 = 8;
                闪避种族值 = 3;
            }
            else if (petInfo.五行.Equals("水"))
            {
                生命种族值 = 21;
                魔法种族值 = 6;
                攻击种族值 = 3;
                防御种族值 = 2;
                命中种族值 = 3;
                速度种族值 = 9;
                闪避种族值 = 5;
            }
            else if (petInfo.五行.Equals("火"))
            {
                生命种族值 = 20;
                魔法种族值 = 6;
                攻击种族值 = 5;
                防御种族值 = 1;
                命中种族值 = 4;
                速度种族值 = 10;
                闪避种族值 = 3;
            }
            else if (petInfo.五行.Equals("土"))
            {
                生命种族值 = 23;
                魔法种族值 = 6;
                攻击种族值 = 3;
                防御种族值 = 4;
                命中种族值 = 4;
                速度种族值 = 6;
                闪避种族值 = 3;
            }
            else if (petInfo.五行.Equals("萌"))
            {
                生命种族值 = 52;
                魔法种族值 = 8;
                攻击种族值 = 13;
                防御种族值 = 5;
                闪避种族值 = 5;
                速度种族值 = 16;
                命中种族值 = 8;
            }
            else if (petInfo.五行.Equals("灵"))
            {
                生命种族值 = 54;
                魔法种族值 = 9;
                攻击种族值 = 15;
                防御种族值 = 7;
                闪避种族值 = 7;
                速度种族值 = 18;
                命中种族值 = 10;
            }
            else if (petInfo.五行.Equals("次元"))
            {
                生命种族值 = 50;
                魔法种族值 = 8;
                攻击种族值 = 12;
                防御种族值 = 4;
                闪避种族值 = 4;
                速度种族值 = 15;
                命中种族值 = 8;
            }else if (petInfo.五行.Equals("巫"))
            {
                生命种族值 = 30;
                魔法种族值 = 5;
                攻击种族值 = 5;
                防御种族值 = 3;
                命中种族值 = 5;
                闪避种族值 = 3;
                速度种族值 = 8;
            }
            else //定制的种族值
            {
                生命种族值 = 52;
                魔法种族值 = 7;
                攻击种族值 = 11;
                防御种族值 = 4;
                闪避种族值 = 4;
                速度种族值 = 15;
                命中种族值 = 7;
            }
            #endregion
            
            #region 境界计算代码
            //这里好像是宠物属性计算
            
          

          
                宠物.生命 = CalcBaseAttribute(petInfo, petInfo.生命, 生命种族值);
                宠物.魔法 = CalcBaseAttribute(petInfo, petInfo.魔法, 魔法种族值);
                宠物.攻击 = CalcBaseAttribute(petInfo, petInfo.攻击, 攻击种族值);
                宠物.防御 = CalcBaseAttribute(petInfo, petInfo.防御, 防御种族值);
                宠物.命中 = CalcBaseAttribute(petInfo, petInfo.命中, 命中种族值);
                宠物.闪避 = CalcBaseAttribute(petInfo, petInfo.闪避, 闪避种族值);
                宠物.速度 = CalcBaseAttribute(petInfo, petInfo.速度, 速度种族值);
            宠物.最大生命 = 宠物.生命;
            宠物.最大魔法 = 宠物.魔法;

            #endregion

            #region 成长作弊判定
            // 使用动态成长上限进行检查
            double maxGrowth = petInfo.GetGrowthLimit();
            if (Convert.ToDouble(petInfo.成长) > maxGrowth && !gw)//宠物成长警告
            {
                AntiCheat.CheatCodeMsg("041");
                AntiCheat.PunishmentProcess(2);
            }
            #endregion
          
            #region 反作弊判断，判断是否大于极限值的属性
            if (Convert.ToDouble(petInfo.攻击) > OneNum)
            {
                AbnormalAttributeProcess();
            }

            if (Convert.ToDouble(petInfo.防御) > OneNum)
            {
                AbnormalAttributeProcess();
            }

            if (Convert.ToDouble(petInfo.命中) > OneNum)
            {
                AbnormalAttributeProcess();
            }

            if (Convert.ToDouble(petInfo.闪避) > OneNum)
            {
                AbnormalAttributeProcess();
            }
            #endregion

            if (calc)//非涅槃时计算属性
            {
                /****
                           * 
                           * 
                           * 攻击
                              命中
                              防御
                              速度
                              闪避
                              生命
                              魔法
                              加深
                              抵消
                              吸血
                              吸魔
                           * ***/
                #region 神兵计算
                if(petInfo.五行 != "巫")
                {
                    var sb = shenbing.calcALL();
                    生命百分比 += sb["生命"];
                    魔法百分比 += sb["魔法"];
                    攻击百分比 += sb["攻击"];
                    防御百分比 += sb["防御"];
                    命中百分比 += sb["命中"];
                    闪避百分比 += sb["闪避"];
                    速度百分比 += sb["速度"];
                    宠物.加深 = (Convert.ToDouble(宠物.加深) +
                                Convert.ToDouble(sb["加深"])).ToString();
                    宠物.吸血 = (Convert.ToDouble(宠物.加深) +
                               Convert.ToDouble(sb["吸血"])).ToString();
                    宠物.抵消 = (Convert.ToDouble(宠物.加深) +
                              Convert.ToDouble(sb["抵消"])).ToString();
                    宠物.吸魔 = (Convert.ToDouble(宠物.加深) +
                              Convert.ToDouble(sb["吸魔"])).ToString();
                }
                

                #endregion
                #region 魂器计算
                var hq = hunqi.calcALL();
                生命百分比 += hq["生命"];
                魔法百分比 += hq["魔法"];
                攻击百分比 += hq["攻击"];
                防御百分比 += hq["防御"];
                命中百分比 += hq["命中"];
                闪避百分比 += hq["闪避"];
                速度百分比 += hq["速度"];
                宠物.加深 = (Convert.ToDouble(宠物.加深) +
                            Convert.ToDouble(hq["加深"])).ToString();
                宠物.吸血 = (Convert.ToDouble(宠物.加深) +
                           Convert.ToDouble(hq["吸血"])).ToString();
                宠物.抵消 = (Convert.ToDouble(宠物.加深) +
                          Convert.ToDouble(hq["抵消"])).ToString();
                宠物.吸魔 = (Convert.ToDouble(宠物.加深) +
                          Convert.ToDouble(hq["吸魔"])).ToString();

                #endregion

                #region 龙珠计算
                string lzname;
                var lz = longzhu.calcALL(out lzname);
                生命百分比 += lz["生命"];
                魔法百分比 += lz["魔法"];
                攻击百分比 += lz["攻击"];
                防御百分比 += lz["防御"];
                命中百分比 += lz["命中"];
                闪避百分比 += lz["闪避"];
                速度百分比 += lz["速度"];
                宠物.加深 = (Convert.ToDouble(宠物.加深) +
                            Convert.ToDouble(lz["加深"])).ToString();
                宠物.吸血 = (Convert.ToDouble(宠物.加深) +
                           Convert.ToDouble(lz["吸血"])).ToString();
                宠物.抵消 = (Convert.ToDouble(宠物.加深) +
                          Convert.ToDouble(lz["抵消"])).ToString();
                宠物.吸魔 = (Convert.ToDouble(宠物.加深) +
                          Convert.ToDouble(lz["吸魔"])).ToString();

                #endregion
                #region 皮肤计算

                if (petInfo.五行 != "巫")
                {
                    var pf = pifu.calcALL();
                    生命百分比 += pf["生命"];
                    魔法百分比 += pf["魔法"];
                    攻击百分比 += pf["攻击"];
                    防御百分比 += pf["防御"];
                    命中百分比 += pf["命中"];
                    闪避百分比 += pf["闪避"];
                    速度百分比 += pf["速度"];
                    宠物.加深 = (Convert.ToDouble(宠物.加深) +
                                Convert.ToDouble(pf["加深"])).ToString();
                    宠物.吸血 = (Convert.ToDouble(宠物.加深) +
                               Convert.ToDouble(pf["吸血"])).ToString();
                    宠物.抵消 = (Convert.ToDouble(宠物.加深) +
                              Convert.ToDouble(pf["抵消"])).ToString();
                    宠物.吸魔 = (Convert.ToDouble(宠物.加深) +
                              Convert.ToDouble(pf["吸魔"])).ToString();
                }
                

                #endregion
                #region 称号属性计算
                if (Tmp != null && petInfo.五行!="巫")
                {
                    foreach (string key in Tmp.Keys)
                    {
                        if (key.Equals("生命"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                生命百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                生命增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }

                        if (key.Equals("魔法"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                魔法百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                魔法增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }

                        if (key.Equals("攻击"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                攻击百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                攻击增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }

                        if (key.Equals("防御"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                防御百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                防御增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }

                        if (key.Equals("命中"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                命中百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                命中增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }

                        if (key.Equals("闪避"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                闪避百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                闪避增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }

                        if (key.Equals("速度"))
                        {
                            if (Tmp[key].Contains("."))
                            {
                                速度百分比 += Convert.ToDouble(Tmp[key]);
                            }
                            else
                            {
                                速度增量 += Convert.ToInt64(Tmp[key]);
                            }
                        }
                    }
                }
                #endregion

                #region 技能属性计算
                List<SkillInfo> 技能信息 = 宠物.信息;
                foreach (SkillInfo 技能 in 技能信息)
                {
                    SkillConfig 技能配置 = 技能.信息;
                    if (技能配置 == null) continue;
                    switch (技能配置.技能附带效果)
                    {
                        case "攻击":
                            攻击百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "命中":
                            命中百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "防御":
                            防御百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "速度":
                            速度百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "闪避":
                            闪避百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "生命":
                            生命百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "魔法":
                            魔法百分比 += CalcSkill(技能配置.附带效果增量, 技能.技能等级);
                            break;
                        case "加深":
                            宠物.加深 = CalcSkill(技能配置.附带效果增量, 技能.技能等级, 宠物.加深).ToString(CultureInfo.InvariantCulture);
                            break;
                        case "抵消":
                            宠物.抵消 = CalcSkill(技能配置.附带效果增量, 技能.技能等级, 宠物.抵消).ToString(CultureInfo.InvariantCulture);
                            break;
                        case "吸血":
                            宠物.吸血 = CalcSkill(技能配置.附带效果增量, 技能.技能等级, 宠物.吸血).ToString(CultureInfo.InvariantCulture);
                            break;
                        case "吸魔":
                            宠物.吸魔 = CalcSkill(技能配置.附带效果增量, 技能.技能等级, 宠物.吸魔).ToString(CultureInfo.InvariantCulture);
                            break;
                    }
                }
                #endregion
            }

            #region 装备属性计算
            List<EquipmentInfo> 装备信息 = new DataProcess().GetPetEquipment(petInfo.宠物序号);
            List<string> 已计算的套装 = new List<string>();
            //这里是计算套装属性的
            int q1 = 0;
            int q2 = 0;
            int q3 = 0;
            int q4 = 0;
            List<String> GList = new List<string>();//在下面的循环中先把宝石拿出来放入到这里，最后统一计算
            foreach (EquipmentInfo 装备 in 装备信息)
            {
                EquipmentType 类型 = new DataProcess().GetAET(装备.类ID);
                if (装备.宝石列表 != null && 装备.宝石列表.Count > 0) {
                    GList.AddRange(装备.宝石列表);
                }
                bool 计算套装 = true;
                foreach (string 记录 in 已计算的套装)
                {
                    if (记录 == 类型.suitID)
                    {
                        计算套装 = false;
                        break;
                    }

                    if (null == 类型.suitID)
                    {
                        计算套装 = false;
                        break;
                    }
                }

                if (计算套装)
                {
                    已计算的套装.Add(类型.suitID);
                    int c = -1;
                    suits 套装 = new DataProcess().GetAppointedSuit(类型.suitID);
                    foreach (EquipmentInfo 装备1 in 装备信息)
                    {
                        EquipmentType type = new DataProcess().GetAET(装备1.类ID);
                        if (type.suitID == "") continue;
                        if (type != null && type.suitID != null && type.suitID.Equals(类型.suitID))
                        {
                            /****
                             * 
                             * 
                             * 攻击
                                命中
                                防御
                                速度
                                闪避
                                生命
                                魔法
                                加深
                                抵消
                                吸血
                                吸魔
                             * ***/
                            if (c > -1)
                            {
                                suit 具体属性 = 套装.套装属性[c];
                                switch (具体属性.Type)
                                {
                                    case "攻击":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            攻击百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            攻击增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "命中":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            命中百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            命中增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "防御":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            防御百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            防御增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "速度":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            速度百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            速度增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "闪避":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            闪避百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            闪避增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "生命":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            生命百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            生命增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "魔法":
                                        if (具体属性.addNump.IndexOf(".", StringComparison.Ordinal) != -1)
                                        {
                                            魔法百分比 += Convert.ToDouble(具体属性.addNump);
                                        }
                                        else
                                        {
                                            魔法增量 += Convert.ToInt64(具体属性.addNump);
                                        }

                                        break;
                                    case "加深":
                                        宠物.加深 = (Convert.ToDouble(宠物.加深) + Convert.ToDouble(具体属性.addNump)).ToString(
                                            CultureInfo.InvariantCulture);
                                        break;
                                    case "抵消":
                                        宠物.抵消 = (Convert.ToDouble(宠物.抵消) + Convert.ToDouble(具体属性.addNump)).ToString(
                                            CultureInfo.InvariantCulture);
                                        break;
                                    case "吸血":
                                        宠物.吸血 = (Convert.ToDouble(宠物.吸血) + Convert.ToDouble(具体属性.addNump)).ToString(
                                            CultureInfo.InvariantCulture);
                                        break;
                                    case "吸魔":
                                        宠物.吸魔 = (Convert.ToDouble(宠物.吸魔) + Convert.ToDouble(具体属性.addNump)).ToString(
                                            CultureInfo.InvariantCulture);
                                        break;
                                }
                            }

                            c++;
                        }
                    }
                }

                short wxlb = 0;

                if (!装备.类型.Equals("法宝") && !装备.类型.Equals("卡牌左") && !装备.类型.Equals("卡牌右"))
                {
                    if (!string.IsNullOrEmpty(装备.WX) && !装备.WX.Equals("无"))
                    {
                        if (装备.WX.Equals(EquipmentProcess.EquimentAttribute[类型.主属性]))
                        {
                            wxlb = 2;
                        }
                        else if (装备.WX.Equals(EquipmentProcess.Restraint[类型.主属性]))
                        {
                            wxlb = 1;
                        }
                    }
                }

                short lv = Convert.ToInt16(装备.强化);
                if (CalcOrNot(类型.防御))
                {
                    string 值 = 类型.防御;
                    if (类型.主属性 == "防御")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            防御百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            防御增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            防御百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            防御增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (CalcOrNot(类型.攻击))
                {
                    string 值 = 类型.攻击;
                    if (类型.主属性 == "攻击")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            攻击百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            攻击增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            攻击百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            攻击增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (CalcOrNot(类型.魔法))
                {
                    string 值 = 类型.魔法;
                    if (类型.主属性 == "魔法")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            魔法百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            魔法增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            魔法百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            魔法增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (CalcOrNot(类型.闪避))
                {
                    string 值 = 类型.闪避;
                    if (类型.主属性 == "闪避")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            闪避百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            闪避增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            闪避百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            闪避增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (CalcOrNot(类型.生命))
                {
                    string 值 = 类型.生命;
                    if (类型.主属性 == "生命")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            生命百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            生命增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            生命百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            生命增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (CalcOrNot(类型.速度))
                {
                    string 值 = 类型.速度;
                    if (类型.主属性 == "速度")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            速度百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            速度增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            速度百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            速度增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (CalcOrNot(类型.命中))
                {
                    string 值 = 类型.命中;
                    if (类型.主属性 == "命中")
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            命中百分比 += CalcEquipment_D(值, lv, wxlb);
                        }
                        else
                        {
                            命中增量 += CalcEquipment_L(值, lv, wxlb);
                        }
                    }
                    else
                    {
                        if (值.IndexOf(".", StringComparison.Ordinal) != -1)
                        {
                            命中百分比 += Convert.ToDouble(值);
                        }
                        else
                        {
                            命中增量 += Convert.ToInt64(值);
                        }
                    }
                }

                if (装备.类型.Equals("灵饰") && 装备.LSSX != null)
                {

                    string[] cfgs = 装备.LSSX.Split('|');
                    foreach (string cfg in cfgs)
                    {
                        if (cfg.Equals("攻击"))
                        {
                            攻击百分比 += NumEncrypt.零点零二();
                        }

                        if (cfg.Equals("防御"))
                        {
                            防御百分比 += NumEncrypt.零点零二();
                        }

                        if (cfg.Equals("命中"))
                        {
                            命中百分比 += NumEncrypt.零点零二();
                        }

                        if (cfg.Equals("速度"))
                        {
                            速度百分比 += NumEncrypt.零点零二();
                        }

                        if (cfg.Equals("闪避"))
                        {
                            闪避百分比 += NumEncrypt.零点零二();
                        }

                        if (cfg.Equals("生命"))
                        {
                            生命百分比 += NumEncrypt.零点零二();
                        }

                        if (cfg.Equals("魔法"))
                        {
                            魔法百分比 += NumEncrypt.零点零二();
                        }
                    }
                }

                if (装备.类型.Equals("法宝"))
                {
                    宠物.TalismanState = 类型.SpecialAffect;
                    //Console.WriteLine(宠物.TalismanState);
                }



                if (CalcOrNot(类型.吸魔))
                {
                    宠物.吸魔 = (Convert.ToDouble(宠物.吸魔) + Convert.ToDouble(类型.吸魔)).ToString(CultureInfo.InvariantCulture);
                }

                if (CalcOrNot(类型.吸血))
                {
                    宠物.吸血 = (Convert.ToDouble(宠物.吸血) + Convert.ToDouble(类型.吸血)).ToString(CultureInfo.InvariantCulture);
                }

                if (CalcOrNot(类型.抵消))
                {
                    宠物.抵消 = (Convert.ToDouble(宠物.抵消) + Convert.ToDouble(类型.抵消)).ToString(CultureInfo.InvariantCulture);
                }

                if (CalcOrNot(类型.加深))
                {
                    宠物.加深 = (Convert.ToDouble(宠物.加深) + Convert.ToDouble(类型.加深)).ToString(CultureInfo.InvariantCulture);
                }

                if (lv == 20)
                {
                    q4 += 1;
                }
                else if (lv >= 15 && lv < 20)
                {
                    q3 += 1;
                }
                else if (lv >= 10 && lv < 15)
                {
                    q2 += 1;
                }
                else if (lv >= 5 && lv < 10)
                {
                    q1 += 1;
                }
            }
            #endregion

            #region 宝石属性计算
            if(petInfo.五行 != "巫")
            { 
                foreach (var g in GList) {
                var ginfo = Gemstone.getGemstone(g);
                if (ginfo != null) {
                    if (ginfo.upType.Equals("攻击"))
                    {
                        攻击百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("防御"))
                    {
                        防御百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("魔法"))
                    {
                        魔法百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("闪避"))
                    {
                        闪避百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("生命"))
                    {
                        生命百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("速度"))
                    {
                        速度百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("命中"))
                    {
                        命中百分比 += ginfo.upNum;
                    }
                    else if (ginfo.upType.Equals("加深"))
                    {
                        宠物.加深 = (Convert.ToDouble(宠物.加深) + ginfo.upNum).ToString();
                    }
                    else if (ginfo.upType.Equals("吸血"))
                    {
                        宠物.吸血 = (Convert.ToDouble(宠物.吸血) + ginfo.upNum).ToString();
                    }
                    else if (ginfo.upType.Equals("抵消"))
                    {
                        宠物.抵消 = (Convert.ToDouble(宠物.抵消) + ginfo.upNum).ToString();
                    }
                }

                }
            }
            #endregion

            if (calc)//魔法卡加成,这个没有加判断导致涅槃属性爆炸,这个锅是残酷干的
            {
                #region 魔法卡属性计算
                var CLIST = new DataProcess().ReadUserInfo().CardList;
                if (CLIST != null && petInfo.五行!="巫")
                {
                    foreach (var g in CLIST)
                    {
                        var ginfo = CardInfo.GetCardInfo(g);
                        if (ginfo != null)
                        {
                            if (ginfo.upType.Equals("攻击"))
                            {
                                攻击百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("防御"))
                            {
                                防御百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("魔法"))
                            {
                                魔法百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("闪避"))
                            {
                                闪避百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("生命"))
                            {
                                生命百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("速度"))
                            {
                                速度百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("命中"))
                            {
                                命中百分比 += ginfo.upNum;
                            }
                            else if (ginfo.upType.Equals("加深"))
                            {
                                宠物.加深 = (Convert.ToDouble(宠物.加深) + ginfo.upNum).ToString();
                            }
                            else if (ginfo.upType.Equals("吸血"))
                            {
                                宠物.吸血 = (Convert.ToDouble(宠物.吸血) + ginfo.upNum).ToString();
                            }
                            else if (ginfo.upType.Equals("抵消"))
                            {
                                宠物.抵消 = (Convert.ToDouble(宠物.抵消) + ginfo.upNum).ToString();
                            }
                        }

                    }
                }
                #endregion
                #region 魂宠属性计算
                var HCLIST = new DataProcess().ReadUserInfo().魂宠;
                if (HCLIST != null && petInfo.五行!="巫")
                {

                    var ginfo = soulPet.getSoulPet(HCLIST);
                    if (ginfo != null)
                    {
                        if (ginfo.攻击 != "0" && ginfo.攻击 != null)
                        {
                            if (ginfo.攻击.Contains("."))
                            {
                                攻击百分比 += Convert.ToDouble(ginfo.攻击);
                            }
                            else
                            {
                                攻击增量 += Convert.ToInt64(ginfo.攻击);
                            }
                        }
                        if (ginfo.防御 != "0" && ginfo.防御 != null)
                        {
                            if (ginfo.防御.Contains("."))
                            {
                                防御百分比 += Convert.ToDouble(ginfo.防御);
                            }
                            else
                            {
                                防御增量 += Convert.ToInt64(ginfo.防御);
                            }
                        }
                        if (ginfo.魔法 != "0" && ginfo.魔法 != null)
                        {
                            if (ginfo.魔法.Contains("."))
                            {
                                魔法百分比 += Convert.ToDouble(ginfo.魔法);
                            }
                            else
                            {
                                魔法增量 += Convert.ToInt64(ginfo.魔法);
                            }
                        }
                        if (ginfo.闪避 != "0" && ginfo.闪避 != null)
                        {
                            if (ginfo.闪避.Contains("."))
                            {
                                闪避百分比 += Convert.ToDouble(ginfo.闪避);
                            }
                            else
                            {
                                闪避增量 += Convert.ToInt64(ginfo.闪避);
                            }
                        }
                        if (ginfo.生命 != "0" && ginfo.生命 != null)
                        {
                            if (ginfo.生命.Contains("."))
                            {
                                生命百分比 += Convert.ToDouble(ginfo.生命);
                            }
                            else
                            {
                                生命增量 += Convert.ToInt64(ginfo.生命);
                            }
                        }
                        if (ginfo.速度 != "0" && ginfo.速度 != null)
                        {
                            if (ginfo.速度.Contains("."))
                            {
                                速度百分比 += Convert.ToDouble(ginfo.速度);
                            }
                            else
                            {
                                速度增量 += Convert.ToInt64(ginfo.速度);
                            }
                        }
                        if (ginfo.命中 != "0" && ginfo.命中 != null)
                        {
                            if (ginfo.命中.Contains("."))
                            {
                                命中百分比 += Convert.ToDouble(ginfo.命中);
                            }
                            else
                            {
                                命中增量 += Convert.ToInt64(ginfo.命中);
                            }
                        }

                        if (ginfo.加深 != "0" && ginfo.加深 != null)
                        {
                            宠物.加深 = (Convert.ToDouble(宠物.加深) + Convert.ToDouble(ginfo.加深)).ToString();
                        }
                        if (ginfo.吸血 != "0" && ginfo.吸血 != null)
                        {
                            宠物.吸血 = (Convert.ToDouble(宠物.吸血) + Convert.ToDouble(ginfo.吸血)).ToString();
                        }
                        if (ginfo.吸魔 != "0" && ginfo.吸魔 != null)
                        {
                            宠物.吸魔 = (Convert.ToDouble(宠物.吸魔) + Convert.ToDouble(ginfo.吸魔)).ToString();
                        }
                        if (ginfo.抵消 != "0" && ginfo.抵消 != null)
                        {
                            宠物.抵消 = (Convert.ToDouble(宠物.抵消) + Convert.ToDouble(ginfo.抵消)).ToString();
                        }
                    }


                }
                #endregion
            }
            
            #region 强化连锁计算
            if (q4 == 10)
            {
                攻击百分比 += NumEncrypt.零点五();
                防御百分比 += NumEncrypt.零点五();
                魔法百分比 += NumEncrypt.零点五();
                闪避百分比 += NumEncrypt.零点五();
                生命百分比 += NumEncrypt.零点五();
                速度百分比 += NumEncrypt.零点五();
                命中百分比 += NumEncrypt.零点五();
            }
            else if (q3 == 10)
            {
                攻击百分比 += NumEncrypt.零点三();
                防御百分比 += NumEncrypt.零点三();
                魔法百分比 += NumEncrypt.零点三();
                闪避百分比 += NumEncrypt.零点三();
                生命百分比 += NumEncrypt.零点三();
                速度百分比 += NumEncrypt.零点三();
                命中百分比 += NumEncrypt.零点三();
            }
            else if (q2 == 10)
            {
                攻击百分比 += NumEncrypt.零点一五();
                防御百分比 += NumEncrypt.零点一五();
                魔法百分比 += NumEncrypt.零点一五();
                闪避百分比 += NumEncrypt.零点一五();
                生命百分比 += NumEncrypt.零点一五();
                速度百分比 += NumEncrypt.零点一五();
                命中百分比 += NumEncrypt.零点一五();
            }
            else if (q1 == 10)
            {
                攻击百分比 += NumEncrypt.零点零五();
                防御百分比 += NumEncrypt.零点零五();
                魔法百分比 += NumEncrypt.零点零五();
                闪避百分比 += NumEncrypt.零点零五();
                生命百分比 += NumEncrypt.零点零五();
                速度百分比 += NumEncrypt.零点零五();
                命中百分比 += NumEncrypt.零点零五();
            }
            #endregion

            #region 属性最终计算
            宠物.闪避 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.闪避) * (1 + 闪避百分比)) + 闪避增量).ToString();
            宠物.攻击 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.攻击) * (1 + 攻击百分比)) + 攻击增量).ToString();
            宠物.命中 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.命中) * (1 + 命中百分比)) + 命中增量).ToString();
            宠物.最大魔法 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.最大魔法) * (1 + 魔法百分比)) + 魔法增量).ToString();
            宠物.最大生命 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.最大生命) * (1 + 生命百分比)) + 生命增量).ToString();
            宠物.速度 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.速度) * (1 + 速度百分比)) + 速度增量).ToString();
            宠物.防御 = Convert.ToInt64(Convert.ToInt64(Convert.ToDouble(宠物.防御) * (1 + 防御百分比)) + 防御增量).ToString();
            int 最低属性 = DataProcess.GetInt("47EC908C9DAE"); //-10000
            #endregion

            #region 属性溢出处理
            if (Convert.ToInt64(宠物.生命) < 最低属性)
            {
                宠物.生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.魔法) < 最低属性)
            {
                宠物.魔法 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.最大魔法) < 最低属性)
            {
                宠物.最大魔法 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.生命) < 最低属性)
            {
                宠物.生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.最大生命) < 最低属性)
            {
                宠物.最大生命 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.攻击) < 最低属性)
            {
                宠物.攻击 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.防御) < 最低属性)
            {
                宠物.防御 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.闪避) < 最低属性)
            {
                宠物.闪避 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.速度) < 最低属性)
            {
                宠物.速度 = long.MaxValue.ToString();
            }

            if (Convert.ToInt64(宠物.命中) < 最低属性)
            {
                宠物.命中 = long.MaxValue.ToString();
            }
            #endregion
            
            #region 最终结果赋值处理
            宠物.宠物名字 = petInfo.宠物名字;
            宠物.宠物序号 = petInfo.宠物序号;
            宠物.当前经验 = petInfo.当前经验;
            宠物.等级 = petInfo.等级;
            宠物.生命 = 宠物.最大生命;
            宠物.魔法 = 宠物.最大魔法;
            宠物.五行 = petInfo.五行;
            宠物.形象 = petInfo.形象;
            宠物.状态 = petInfo.状态;
            宠物.指定五行 = petInfo.指定五行;
            宠物.自定义宠物名字 = petInfo.自定义宠物名字;
            宠物.已进化次数 = petInfo.已进化次数;
            宠物.境界 = petInfo.境界;
            宠物.TalismanState = String.IsNullOrEmpty(宠物.TalismanState) ? "" : 宠物.TalismanState;

            //宠物.被抽取 = 宠物属性.被抽取;
            宠物.成长 = Math.Round(Convert.ToDouble(petInfo.成长), 2).ToString(CultureInfo.InvariantCulture);
            if (cache && PetInfoBuffer == null && PetInfoCache == null)
            {
                //LogSystem.JoinLog(LogSystem.EventKind.宠物缓存,"建立成功");
                PetInfoBuffer = new[] {petInfo.宠物序号, petInfo.等级, petInfo.形象, petInfo.技能列表, petInfo.成长};
                //PetInfoCacheMd5 = 宠物.ToMd5();
                PetInfoCache = 宠物;
            }
            #endregion
            
            return 宠物;
        }

        internal static List<PetInfo> CalcPetsAttribute(List<PetInfo> 宠物属性)
        {
            string mainPet = new DataProcess().ReadUserInfo().主宠物;
            for (int i = 0; i < 宠物属性.Count; i++)
            {
                if (宠物属性[i].宠物序号 == mainPet)
                {
                    宠物属性[i] = CalcPetAttribute(宠物属性[i], true);
                }
            }

            return 宠物属性;
        }

        private static void AbnormalAttributeProcess()
        {
            AntiCheat.CheatCodeMsg("041");
            AntiCheat.PunishmentProcess(2);
        }
    }
}
