﻿namespace Admin
{
    partial class 任务管理
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.label1 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.任务序号 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.任务名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.指定宠物 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.允许重复 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.前置任务 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.任务列表菜单 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.删除选中任务ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.任务模板 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.任务模板ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.修炼仙册188个ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.修炼仙册288个ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.重生四阶1个ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.任务条件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.水晶200万ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.水晶300万ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.水晶500万ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.强化石300万ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.强化石500万ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.三色龙鳞200个ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.三个龙鳞500个ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.任务击杀条件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.通过玲珑城ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.觉醒任务3修正ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.linkLabel7 = new System.Windows.Forms.LinkLabel();
            this.button5 = new System.Windows.Forms.Button();
            this.textBox7 = new System.Windows.Forms.TextBox();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.linkLabel10 = new System.Windows.Forms.LinkLabel();
            this.dataGridView2 = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.label6 = new System.Windows.Forms.Label();
            this.button4 = new System.Windows.Forms.Button();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.button2 = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.任务类型说明 = new System.Windows.Forms.LinkLabel();
            this.linkLabel6 = new System.Windows.Forms.LinkLabel();
            this.button3 = new System.Windows.Forms.Button();
            this.linkLabel5 = new System.Windows.Forms.LinkLabel();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.linkLabel4 = new System.Windows.Forms.LinkLabel();
            this.linkLabel3 = new System.Windows.Forms.LinkLabel();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.button1 = new System.Windows.Forms.Button();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.奖励 = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.宠物 = new System.Windows.Forms.TextBox();
            this.名字 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.序号 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.nextTask = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.任务列表菜单.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.任务模板.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView2)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(15, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 8;
            this.label1.Text = "快速搜索:";
            this.label1.Click += new System.EventHandler(this.label1_Click);
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(80, 8);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(236, 21);
            this.textBox1.TabIndex = 7;
            this.textBox1.TextChanged += new System.EventHandler(this.textBox1_TextChanged);
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.AllowUserToResizeColumns = false;
            this.dataGridView1.AllowUserToResizeRows = false;
            this.dataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dataGridView1.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dataGridView1.ColumnHeadersHeight = 29;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.任务序号,
            this.任务名,
            this.指定宠物,
            this.允许重复,
            this.前置任务});
            this.dataGridView1.ContextMenuStrip = this.任务列表菜单;
            this.dataGridView1.ImeMode = System.Windows.Forms.ImeMode.Katakana;
            this.dataGridView1.Location = new System.Drawing.Point(12, 35);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.RowHeadersVisible = false;
            this.dataGridView1.RowHeadersWidth = 51;
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.dataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView1.Size = new System.Drawing.Size(604, 353);
            this.dataGridView1.TabIndex = 6;
            this.dataGridView1.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellContentClick);
            // 
            // 任务序号
            // 
            this.任务序号.DataPropertyName = "任务序号";
            this.任务序号.HeaderText = "任务序号";
            this.任务序号.MinimumWidth = 6;
            this.任务序号.Name = "任务序号";
            this.任务序号.ReadOnly = true;
            this.任务序号.Width = 101;
            // 
            // 任务名
            // 
            this.任务名.DataPropertyName = "任务名";
            this.任务名.HeaderText = "任务名";
            this.任务名.MinimumWidth = 6;
            this.任务名.Name = "任务名";
            this.任务名.ReadOnly = true;
            this.任务名.Width = 200;
            // 
            // 指定宠物
            // 
            this.指定宠物.DataPropertyName = "指定宠物";
            this.指定宠物.HeaderText = "指定宠物";
            this.指定宠物.MinimumWidth = 6;
            this.指定宠物.Name = "指定宠物";
            this.指定宠物.Width = 125;
            // 
            // 允许重复
            // 
            this.允许重复.DataPropertyName = "允许重复";
            this.允许重复.HeaderText = "允许重复";
            this.允许重复.MinimumWidth = 6;
            this.允许重复.Name = "允许重复";
            this.允许重复.Width = 101;
            // 
            // 前置任务
            // 
            this.前置任务.DataPropertyName = "前置任务";
            this.前置任务.HeaderText = "前置任务";
            this.前置任务.MinimumWidth = 6;
            this.前置任务.Name = "前置任务";
            this.前置任务.Width = 125;
            // 
            // 任务列表菜单
            // 
            this.任务列表菜单.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.删除选中任务ToolStripMenuItem});
            this.任务列表菜单.Name = "任务列表菜单";
            this.任务列表菜单.Size = new System.Drawing.Size(149, 26);
            // 
            // 删除选中任务ToolStripMenuItem
            // 
            this.删除选中任务ToolStripMenuItem.Name = "删除选中任务ToolStripMenuItem";
            this.删除选中任务ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.删除选中任务ToolStripMenuItem.Text = "删除选中任务";
            this.删除选中任务ToolStripMenuItem.Click += new System.EventHandler(this.删除选中任务ToolStripMenuItem_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.ContextMenuStrip = this.任务模板;
            this.groupBox1.Controls.Add(this.nextTask);
            this.groupBox1.Controls.Add(this.linkLabel7);
            this.groupBox1.Controls.Add(this.button5);
            this.groupBox1.Controls.Add(this.textBox7);
            this.groupBox1.Controls.Add(this.textBox6);
            this.groupBox1.Controls.Add(this.linkLabel10);
            this.groupBox1.Controls.Add(this.dataGridView2);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.button4);
            this.groupBox1.Controls.Add(this.textBox5);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.textBox4);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.button2);
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Controls.Add(this.linkLabel1);
            this.groupBox1.Controls.Add(this.奖励);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.宠物);
            this.groupBox1.Controls.Add(this.名字);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.序号);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Location = new System.Drawing.Point(632, 7);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(647, 407);
            this.groupBox1.TabIndex = 9;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "任务定义";
            this.groupBox1.Enter += new System.EventHandler(this.groupBox1_Enter);
            // 
            // 任务模板
            // 
            this.任务模板.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.任务模板ToolStripMenuItem,
            this.任务条件ToolStripMenuItem,
            this.任务击杀条件ToolStripMenuItem,
            this.觉醒任务3修正ToolStripMenuItem});
            this.任务模板.Name = "任务模板";
            this.任务模板.Size = new System.Drawing.Size(156, 92);
            // 
            // 任务模板ToolStripMenuItem
            // 
            this.任务模板ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.修炼仙册188个ToolStripMenuItem,
            this.修炼仙册288个ToolStripMenuItem,
            this.重生四阶1个ToolStripMenuItem});
            this.任务模板ToolStripMenuItem.Name = "任务模板ToolStripMenuItem";
            this.任务模板ToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.任务模板ToolStripMenuItem.Text = "任务奖励";
            // 
            // 修炼仙册188个ToolStripMenuItem
            // 
            this.修炼仙册188个ToolStripMenuItem.Name = "修炼仙册188个ToolStripMenuItem";
            this.修炼仙册188个ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.修炼仙册188个ToolStripMenuItem.Text = "修炼仙册188个";
            this.修炼仙册188个ToolStripMenuItem.Click += new System.EventHandler(this.修炼仙册188个ToolStripMenuItem_Click);
            // 
            // 修炼仙册288个ToolStripMenuItem
            // 
            this.修炼仙册288个ToolStripMenuItem.Name = "修炼仙册288个ToolStripMenuItem";
            this.修炼仙册288个ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.修炼仙册288个ToolStripMenuItem.Text = "修炼仙册288个";
            this.修炼仙册288个ToolStripMenuItem.Click += new System.EventHandler(this.修炼仙册288个ToolStripMenuItem_Click);
            // 
            // 重生四阶1个ToolStripMenuItem
            // 
            this.重生四阶1个ToolStripMenuItem.Name = "重生四阶1个ToolStripMenuItem";
            this.重生四阶1个ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.重生四阶1个ToolStripMenuItem.Text = "重生·四阶1个";
            this.重生四阶1个ToolStripMenuItem.Click += new System.EventHandler(this.重生四阶1个ToolStripMenuItem_Click);
            // 
            // 任务条件ToolStripMenuItem
            // 
            this.任务条件ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.水晶200万ToolStripMenuItem,
            this.水晶300万ToolStripMenuItem,
            this.水晶500万ToolStripMenuItem,
            this.强化石300万ToolStripMenuItem,
            this.强化石500万ToolStripMenuItem,
            this.三色龙鳞200个ToolStripMenuItem,
            this.三个龙鳞500个ToolStripMenuItem});
            this.任务条件ToolStripMenuItem.Name = "任务条件ToolStripMenuItem";
            this.任务条件ToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.任务条件ToolStripMenuItem.Text = "任务其他条件";
            // 
            // 水晶200万ToolStripMenuItem
            // 
            this.水晶200万ToolStripMenuItem.Name = "水晶200万ToolStripMenuItem";
            this.水晶200万ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.水晶200万ToolStripMenuItem.Text = "水晶200万";
            this.水晶200万ToolStripMenuItem.Click += new System.EventHandler(this.水晶200万ToolStripMenuItem_Click);
            // 
            // 水晶300万ToolStripMenuItem
            // 
            this.水晶300万ToolStripMenuItem.Name = "水晶300万ToolStripMenuItem";
            this.水晶300万ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.水晶300万ToolStripMenuItem.Text = "水晶300万";
            this.水晶300万ToolStripMenuItem.Click += new System.EventHandler(this.水晶300万ToolStripMenuItem_Click);
            // 
            // 水晶500万ToolStripMenuItem
            // 
            this.水晶500万ToolStripMenuItem.Name = "水晶500万ToolStripMenuItem";
            this.水晶500万ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.水晶500万ToolStripMenuItem.Text = "水晶500万";
            this.水晶500万ToolStripMenuItem.Click += new System.EventHandler(this.水晶500万ToolStripMenuItem_Click);
            // 
            // 强化石300万ToolStripMenuItem
            // 
            this.强化石300万ToolStripMenuItem.Name = "强化石300万ToolStripMenuItem";
            this.强化石300万ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.强化石300万ToolStripMenuItem.Text = "强化石300万";
            this.强化石300万ToolStripMenuItem.Click += new System.EventHandler(this.强化石300万ToolStripMenuItem_Click);
            // 
            // 强化石500万ToolStripMenuItem
            // 
            this.强化石500万ToolStripMenuItem.Name = "强化石500万ToolStripMenuItem";
            this.强化石500万ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.强化石500万ToolStripMenuItem.Text = "强化石500万";
            this.强化石500万ToolStripMenuItem.Click += new System.EventHandler(this.强化石500万ToolStripMenuItem_Click);
            // 
            // 三色龙鳞200个ToolStripMenuItem
            // 
            this.三色龙鳞200个ToolStripMenuItem.Name = "三色龙鳞200个ToolStripMenuItem";
            this.三色龙鳞200个ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.三色龙鳞200个ToolStripMenuItem.Text = "三色龙鳞200个";
            this.三色龙鳞200个ToolStripMenuItem.Click += new System.EventHandler(this.三色龙鳞200个ToolStripMenuItem_Click);
            // 
            // 三个龙鳞500个ToolStripMenuItem
            // 
            this.三个龙鳞500个ToolStripMenuItem.Name = "三个龙鳞500个ToolStripMenuItem";
            this.三个龙鳞500个ToolStripMenuItem.Size = new System.Drawing.Size(157, 22);
            this.三个龙鳞500个ToolStripMenuItem.Text = "三个龙鳞500个";
            this.三个龙鳞500个ToolStripMenuItem.Click += new System.EventHandler(this.三个龙鳞500个ToolStripMenuItem_Click);
            // 
            // 任务击杀条件ToolStripMenuItem
            // 
            this.任务击杀条件ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.通过玲珑城ToolStripMenuItem});
            this.任务击杀条件ToolStripMenuItem.Name = "任务击杀条件ToolStripMenuItem";
            this.任务击杀条件ToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.任务击杀条件ToolStripMenuItem.Text = "任务击杀条件";
            // 
            // 通过玲珑城ToolStripMenuItem
            // 
            this.通过玲珑城ToolStripMenuItem.Name = "通过玲珑城ToolStripMenuItem";
            this.通过玲珑城ToolStripMenuItem.Size = new System.Drawing.Size(136, 22);
            this.通过玲珑城ToolStripMenuItem.Text = "通关玲珑城";
            this.通过玲珑城ToolStripMenuItem.Click += new System.EventHandler(this.通过玲珑城ToolStripMenuItem_Click);
            // 
            // 觉醒任务3修正ToolStripMenuItem
            // 
            this.觉醒任务3修正ToolStripMenuItem.Name = "觉醒任务3修正ToolStripMenuItem";
            this.觉醒任务3修正ToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.觉醒任务3修正ToolStripMenuItem.Text = "觉醒任务3修正";
            this.觉醒任务3修正ToolStripMenuItem.Click += new System.EventHandler(this.觉醒任务3修正ToolStripMenuItem_Click);
            // 
            // linkLabel7
            // 
            this.linkLabel7.AutoSize = true;
            this.linkLabel7.Location = new System.Drawing.Point(8, 132);
            this.linkLabel7.Name = "linkLabel7";
            this.linkLabel7.Size = new System.Drawing.Size(29, 12);
            this.linkLabel7.TabIndex = 54;
            this.linkLabel7.TabStop = true;
            this.linkLabel7.Text = "装备";
            this.linkLabel7.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel7_LinkClicked);
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(271, 96);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(41, 62);
            this.button5.TabIndex = 53;
            this.button5.Text = "←";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // textBox7
            // 
            this.textBox7.Location = new System.Drawing.Point(314, 103);
            this.textBox7.Multiline = true;
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new System.Drawing.Size(229, 66);
            this.textBox7.TabIndex = 52;
            this.textBox7.TextChanged += new System.EventHandler(this.textBox7_TextChanged);
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(17, 282);
            this.textBox6.Multiline = true;
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(267, 74);
            this.textBox6.TabIndex = 51;
            this.textBox6.TextChanged += new System.EventHandler(this.textBox6_TextChanged);
            // 
            // linkLabel10
            // 
            this.linkLabel10.AutoSize = true;
            this.linkLabel10.Location = new System.Drawing.Point(261, 19);
            this.linkLabel10.Name = "linkLabel10";
            this.linkLabel10.Size = new System.Drawing.Size(53, 12);
            this.linkLabel10.TabIndex = 50;
            this.linkLabel10.TabStop = true;
            this.linkLabel10.Text = "选择宠物";
            this.linkLabel10.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel10_LinkClicked_1);
            // 
            // dataGridView2
            // 
            this.dataGridView2.AllowUserToAddRows = false;
            this.dataGridView2.AllowUserToResizeColumns = false;
            this.dataGridView2.AllowUserToResizeRows = false;
            this.dataGridView2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dataGridView2.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dataGridView2.ColumnHeadersHeight = 29;
            this.dataGridView2.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn1,
            this.dataGridViewTextBoxColumn2,
            this.dataGridViewTextBoxColumn3});
            this.dataGridView2.ImeMode = System.Windows.Forms.ImeMode.Katakana;
            this.dataGridView2.Location = new System.Drawing.Point(305, 175);
            this.dataGridView2.Name = "dataGridView2";
            this.dataGridView2.RowHeadersVisible = false;
            this.dataGridView2.RowHeadersWidth = 51;
            this.dataGridView2.RowTemplate.Height = 23;
            this.dataGridView2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.dataGridView2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView2.Size = new System.Drawing.Size(335, 226);
            this.dataGridView2.TabIndex = 44;
            this.dataGridView2.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView2_CellContentClick);
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.DataPropertyName = "Type";
            this.dataGridViewTextBoxColumn1.HeaderText = "Type";
            this.dataGridViewTextBoxColumn1.MinimumWidth = 6;
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.Width = 80;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.DataPropertyName = "Num";
            this.dataGridViewTextBoxColumn2.HeaderText = "Num";
            this.dataGridViewTextBoxColumn2.MinimumWidth = 6;
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.Width = 50;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.DataPropertyName = "ID";
            this.dataGridViewTextBoxColumn3.HeaderText = "forID";
            this.dataGridViewTextBoxColumn3.MinimumWidth = 6;
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.Width = 200;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(43, 75);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(137, 12);
            this.label6.TabIndex = 41;
            this.label6.Text = "金币,1000|道具,10000,1";
            this.label6.Click += new System.EventHandler(this.label6_Click);
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(137, 369);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(82, 32);
            this.button4.TabIndex = 49;
            this.button4.Text = "保存任务";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(225, 46);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(113, 21);
            this.textBox5.TabIndex = 48;
            this.textBox5.TextChanged += new System.EventHandler(this.textBox5_TextChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(193, 50);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(35, 12);
            this.label11.TabIndex = 47;
            this.label11.Text = "前置:";
            this.label11.Click += new System.EventHandler(this.label11_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(142, 20);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "指定宠物:";
            this.label4.Click += new System.EventHandler(this.label4_Click);
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(344, 15);
            this.textBox4.Multiline = true;
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(294, 82);
            this.textBox4.TabIndex = 46;
            this.textBox4.TextChanged += new System.EventHandler(this.textBox4_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(312, 21);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(35, 12);
            this.label10.TabIndex = 45;
            this.label10.Text = "说明:";
            this.label10.Click += new System.EventHandler(this.label10_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(26, 369);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(82, 32);
            this.button2.TabIndex = 44;
            this.button2.Text = "加入任务";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.任务类型说明);
            this.groupBox2.Controls.Add(this.linkLabel6);
            this.groupBox2.Controls.Add(this.button3);
            this.groupBox2.Controls.Add(this.linkLabel5);
            this.groupBox2.Controls.Add(this.checkBox1);
            this.groupBox2.Controls.Add(this.linkLabel4);
            this.groupBox2.Controls.Add(this.linkLabel3);
            this.groupBox2.Controls.Add(this.textBox3);
            this.groupBox2.Controls.Add(this.comboBox1);
            this.groupBox2.Controls.Add(this.linkLabel2);
            this.groupBox2.Controls.Add(this.button1);
            this.groupBox2.Controls.Add(this.textBox2);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Location = new System.Drawing.Point(11, 163);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(288, 200);
            this.groupBox2.TabIndex = 42;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "任务目标定义";
            this.groupBox2.Enter += new System.EventHandler(this.groupBox2_Enter);
            // 
            // 任务类型说明
            // 
            this.任务类型说明.AutoSize = true;
            this.任务类型说明.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.任务类型说明.Location = new System.Drawing.Point(-5, 23);
            this.任务类型说明.Name = "任务类型说明";
            this.任务类型说明.Size = new System.Drawing.Size(17, 16);
            this.任务类型说明.TabIndex = 47;
            this.任务类型说明.TabStop = true;
            this.任务类型说明.Text = "?";
            this.任务类型说明.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.任务类型说明_LinkClicked);
            // 
            // linkLabel6
            // 
            this.linkLabel6.AutoSize = true;
            this.linkLabel6.Location = new System.Drawing.Point(229, 62);
            this.linkLabel6.Name = "linkLabel6";
            this.linkLabel6.Size = new System.Drawing.Size(53, 12);
            this.linkLabel6.TabIndex = 46;
            this.linkLabel6.TabStop = true;
            this.linkLabel6.Text = "选择装备";
            this.linkLabel6.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel6_LinkClicked);
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(210, 83);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(62, 30);
            this.button3.TabIndex = 45;
            this.button3.Text = "→";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // linkLabel5
            // 
            this.linkLabel5.AutoSize = true;
            this.linkLabel5.Location = new System.Drawing.Point(150, 102);
            this.linkLabel5.Name = "linkLabel5";
            this.linkLabel5.Size = new System.Drawing.Size(53, 12);
            this.linkLabel5.TabIndex = 44;
            this.linkLabel5.TabStop = true;
            this.linkLabel5.Text = "清楚选中";
            this.linkLabel5.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel5_LinkClicked);
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Location = new System.Drawing.Point(91, 0);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(84, 16);
            this.checkBox1.TabIndex = 43;
            this.checkBox1.Text = "能多次完成";
            this.checkBox1.UseVisualStyleBackColor = true;
            this.checkBox1.CheckedChanged += new System.EventHandler(this.checkBox1_CheckedChanged);
            // 
            // linkLabel4
            // 
            this.linkLabel4.AutoSize = true;
            this.linkLabel4.Location = new System.Drawing.Point(229, 47);
            this.linkLabel4.Name = "linkLabel4";
            this.linkLabel4.Size = new System.Drawing.Size(53, 12);
            this.linkLabel4.TabIndex = 42;
            this.linkLabel4.TabStop = true;
            this.linkLabel4.Text = "选择道具";
            this.linkLabel4.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel4_LinkClicked);
            // 
            // linkLabel3
            // 
            this.linkLabel3.AutoSize = true;
            this.linkLabel3.Location = new System.Drawing.Point(170, 47);
            this.linkLabel3.Name = "linkLabel3";
            this.linkLabel3.Size = new System.Drawing.Size(53, 12);
            this.linkLabel3.TabIndex = 41;
            this.linkLabel3.TabStop = true;
            this.linkLabel3.Text = "选择怪物";
            this.linkLabel3.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel3_LinkClicked);
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(83, 48);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(86, 21);
            this.textBox3.TabIndex = 4;
            this.textBox3.TextChanged += new System.EventHandler(this.textBox3_TextChanged);
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "击杀",
            "收集",
            "等级",
            "金币",
            "元宝",
            "水晶",
            "经验",
            "VIP",
            "至尊VIP",
            "星辰VIP",
            "装备",
            "时之券",
            "积分",
            "宠物",
            "多个主宠",
            "主宠达到成长",
            "扣除成长",
            "扣除成长2",
            "保留成长",
            "威望",
            "地狱",
            "通天",
            "时间",
            "一键完成道具",
            "隐藏主宠提示"});
            this.comboBox1.Location = new System.Drawing.Point(45, 21);
            this.comboBox1.MaxDropDownItems = 16;
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(73, 20);
            this.comboBox1.TabIndex = 0;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Location = new System.Drawing.Point(150, 82);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(53, 12);
            this.linkLabel2.TabIndex = 6;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "清空重来";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(11, 82);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(118, 32);
            this.button1.TabIndex = 5;
            this.button1.Text = "加入目标列表";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(164, 20);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(44, 21);
            this.textBox2.TabIndex = 3;
            this.textBox2.TextChanged += new System.EventHandler(this.textBox2_TextChanged);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(124, 25);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(35, 12);
            this.label8.TabIndex = 2;
            this.label8.Text = "数量:";
            this.label8.Click += new System.EventHandler(this.label8_Click);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(13, 53);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(77, 12);
            this.label9.TabIndex = 1;
            this.label9.Text = "怪物|道具ID:";
            this.label9.Click += new System.EventHandler(this.label9_Click);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(13, 25);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(35, 12);
            this.label7.TabIndex = 1;
            this.label7.Text = "类型:";
            this.label7.Click += new System.EventHandler(this.label7_Click);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Location = new System.Drawing.Point(8, 115);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(29, 12);
            this.linkLabel1.TabIndex = 40;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "道具";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // 奖励
            // 
            this.奖励.Location = new System.Drawing.Point(38, 90);
            this.奖励.Multiline = true;
            this.奖励.Name = "奖励";
            this.奖励.Size = new System.Drawing.Size(229, 72);
            this.奖励.TabIndex = 1;
            this.奖励.TextChanged += new System.EventHandler(this.奖励_TextChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(6, 94);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(35, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "奖励:";
            this.label5.Click += new System.EventHandler(this.label5_Click);
            // 
            // 宠物
            // 
            this.宠物.Location = new System.Drawing.Point(203, 16);
            this.宠物.Name = "宠物";
            this.宠物.Size = new System.Drawing.Size(52, 21);
            this.宠物.TabIndex = 1;
            this.宠物.TextChanged += new System.EventHandler(this.宠物_TextChanged);
            // 
            // 名字
            // 
            this.名字.Location = new System.Drawing.Point(38, 46);
            this.名字.Name = "名字";
            this.名字.Size = new System.Drawing.Size(149, 21);
            this.名字.TabIndex = 1;
            this.名字.TextChanged += new System.EventHandler(this.名字_TextChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(6, 50);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "名字:";
            this.label3.Click += new System.EventHandler(this.label3_Click);
            // 
            // 序号
            // 
            this.序号.Location = new System.Drawing.Point(38, 17);
            this.序号.Name = "序号";
            this.序号.Size = new System.Drawing.Size(82, 21);
            this.序号.TabIndex = 1;
            this.序号.TextChanged += new System.EventHandler(this.序号_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(6, 21);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "序号:";
            this.label2.Click += new System.EventHandler(this.label2_Click);
            // 
            // nextTask
            // 
            this.nextTask.Location = new System.Drawing.Point(124, 15);
            this.nextTask.Name = "nextTask";
            this.nextTask.Size = new System.Drawing.Size(17, 27);
            this.nextTask.TabIndex = 57;
            this.nextTask.Text = ">";
            this.nextTask.UseVisualStyleBackColor = true;
            this.nextTask.Click += new System.EventHandler(this.nextTask_Click);
            // 
            // 任务管理
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1282, 426);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.dataGridView1);
            this.Name = "任务管理";
            this.Text = "任务管理";
            this.Load += new System.EventHandler(this.任务管理_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.任务列表菜单.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.任务模板.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView2)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox 奖励;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox 宠物;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox 名字;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox 序号;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.LinkLabel linkLabel1;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.TextBox textBox3;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ComboBox comboBox1;
        private System.Windows.Forms.LinkLabel linkLabel2;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.LinkLabel linkLabel4;
        private System.Windows.Forms.LinkLabel linkLabel3;
        private System.Windows.Forms.TextBox textBox4;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.CheckBox checkBox1;
        private System.Windows.Forms.TextBox textBox5;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.DataGridView dataGridView2;
        private System.Windows.Forms.LinkLabel linkLabel10;
        private System.Windows.Forms.LinkLabel linkLabel5;
        private System.Windows.Forms.TextBox textBox6;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.Button button5;
        private System.Windows.Forms.TextBox textBox7;
        private System.Windows.Forms.LinkLabel linkLabel6;
        private System.Windows.Forms.ContextMenuStrip 任务模板;
        private System.Windows.Forms.ToolStripMenuItem 任务模板ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 任务条件ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 水晶200万ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 水晶300万ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 水晶500万ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 强化石300万ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 强化石500万ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 三色龙鳞200个ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 三个龙鳞500个ToolStripMenuItem;
        private System.Windows.Forms.LinkLabel linkLabel7;
        private System.Windows.Forms.ToolStripMenuItem 修炼仙册188个ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 修炼仙册288个ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 重生四阶1个ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 任务击杀条件ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 通过玲珑城ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 觉醒任务3修正ToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip 任务列表菜单;
        private System.Windows.Forms.ToolStripMenuItem 删除选中任务ToolStripMenuItem;
        private System.Windows.Forms.DataGridViewTextBoxColumn 任务序号;
        private System.Windows.Forms.DataGridViewTextBoxColumn 任务名;
        private System.Windows.Forms.DataGridViewTextBoxColumn 指定宠物;
        private System.Windows.Forms.DataGridViewTextBoxColumn 允许重复;
        private System.Windows.Forms.DataGridViewTextBoxColumn 前置任务;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.LinkLabel 任务类型说明;
        private System.Windows.Forms.Button nextTask;
    }
}