﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
// ReSharper disable LocalizableElement

namespace Shikong.Pokemon2.PCG
{
    public partial class PlayerHelper : Form
    {
        //自动涅槃统计
        static int 涅槃次数 = 0;
        static double 累计CC = 0;
        static double 平均CC = 0;
        public static double 平均CC_涅槃 = 0;
        public static double 平均CC_进化 = 0;
        static long 消耗经验1 = 0;
        static long 消耗经验2 = 0;
        static string 宠物ID = "";
        static string 副宠道具 = "";
        static string 涅槃道具1 = "";
        static string 涅槃道具2 = "";
        static string 开始时间 = "";
        static string 时间 = "";
        static string 涅槃前CC = "";
        static string 涅槃后CC = "";
        internal static void addCC(double CC,string cc2 ="")
        {
            累计CC = Math.Round(累计CC + CC, 2);
            平均CC = Math.Round(累计CC / 涅槃次数, 2);
            if (cc2 != "") 涅槃后CC = cc2;
        }
        static string GetExp(long exp)
        {
            if (exp >= 1000000000) return (exp / 100000000).ToString("0.##") + "亿";
            else if (exp >= 10000000) return (exp / 10000000).ToString("0.##") + "千万";
            else return exp.ToString();
        }
        public bool getMS()
        {
            return FIGHT_ATK.Checked;
        }
        static void upInfo()
        {
            if (涅槃次数 < 1) return;
            string json = "{\"论坛ID\":\"{nID}\",\"涅槃宠物ID\":\"{pID}\",\"涅槃前CC\":\"{CC1}\",\"涅槃后CC\":\"{CC2}\",\"涅槃次数\":\"{num}\",\"涅槃道具1\":\"{npop1}\",\"涅槃道具2\":\"{npop2}\",\"副宠道具\":\"{popID}\",\"累计增加成长\":\"{addCC}\",\"消耗普通经验\":\"{exp1}\",\"消耗巫族经验\":\"{exp2}\",\"涅槃耗时\":\"{time}\",\"涅槃开始时间\":\"{stime}\",\"涅槃结束时间\":\"{etime}\"}";
            json = json.Replace("{nID}", new DataProcess().ReadUserInfo().论坛ID);
            json = json.Replace("{pID}", 宠物ID != "" ? new DataProcess().获取宠物名称(宠物ID) :"无宠物");
            json = json.Replace("{num}", 涅槃次数.ToString());
            json = json.Replace("{exp1}", 消耗经验1.ToString());
            json = json.Replace("{exp2}", 消耗经验2.ToString());
            json = json.Replace("{popID}", new DataProcess().GetPropName(副宠道具));
            json = json.Replace("{npop1}", 涅槃道具1 == "无" ? "无" : new DataProcess().GetPropName(涅槃道具1));
            json = json.Replace("{npop2}", 涅槃道具2=="无"?"无": new DataProcess().GetPropName(涅槃道具2));
            json = json.Replace("{addCC}", 累计CC.ToString()); ;
            json = json.Replace("{CC1}", 涅槃前CC);
            json = json.Replace("{CC2}", 涅槃后CC);
            string 时间_ = "";
            if (Convert.ToInt64(时间) >= 3600000) 时间_ = (Convert.ToInt64(时间) / 1000 / 60/60).ToString() + "小时";
            else if(Convert.ToInt64(时间) >= 60000) 时间_ = (Convert.ToInt64(时间) / 1000 / 60).ToString() + "分";
            else 时间_ = (Convert.ToInt64(时间) / 1000 ).ToString() + "秒";
            json = json.Replace("{time}", 时间_);
            json = json.Replace("{etime}", DateTime.Now.ToString());
            json = json.Replace("{stime}", 开始时间);
            Thread thread = new Thread(delegate ()
            {
                try
                {
                    new ConvertJson().GetWeb("http://47.57.153.101/updateNP.php", 1, "json=" + json);
                }
                catch (Exception ex)
                {
                    LogSystem.JoinLog(LogSystem.EventKind.加入日志, "函数 玩家助手.涅槃完成 出现错误：【" + ex.StackTrace + "】\r\n【" + ex.Message + "】");
                }
            });
            thread.Start();
            Thread.Sleep(1000);
        }
        public PlayerHelper()
        {
            InitializeComponent();
        }

        internal static Dictionary<string, PetInfo> PetTable = new Dictionary<string, PetInfo>();
        internal static Dictionary<string, string> TaskTable = new Dictionary<string, string>();
        internal static Dictionary<string, string> TaskTable1 = new Dictionary<string, string>();
        private static System.Timers.Timer _autonp;
        internal static Form1 GameForm;
        internal static PlayerHelper Helper;
        private const string Error = "自动涅槃失败：";

        private void timesTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!Char.IsNumber(e.KeyChar) && e.KeyChar != (char) 8)
            {
                e.Handled = true;
            }
        }

        private void vicelvTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!Char.IsNumber(e.KeyChar) && e.KeyChar != (char) 8)
            {
                e.Handled = true;
            }
        }

        private void mainlvTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!Char.IsNumber(e.KeyChar) && e.KeyChar != (char) 8)
            {
                e.Handled = true;
            }
        }

        private void taskTimesTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!Char.IsNumber(e.KeyChar) && e.KeyChar != (char) 8)
            {
                e.Handled = true;
            }
        }

        internal static Dictionary<string, string> NirvanaProp =
            new Dictionary<string, string> {{"涅槃丹", "2016100403"}, {"未提炼的神丹", "2016100402"}, {"天赐神魂", "2016100401"}, { "凤凰羽毛", "2022030901" }, { "七彩蘑菇", "2022033007" } };

        internal static Dictionary<string, string> EvolutionProp = new Dictionary<string, string>
        {
          
            {"玉露结晶", "2016110545"},
            {"天仙玉露", "2016110546"},
            {"强化丹A", "2016110512"},
            {"强化丹B", "2016110513"},
        };

        internal static Dictionary<string, string> ViceProp = new Dictionary<string, string>
        {   {"宝宝龙之卵", "2022033025"},
            {"小神龙琅琊之卵-25cc", "2016100803"},
            {"小神龙琅琊之卵-60cc", "2017080712"},
            {"小神宠礼券", "2016100807"},
            {"女神礼券", "2016100816"},
            {"年兽宝宝之卵-200cc", "2018102503"},
            {"萌系宠礼券", "2016100822"},
            {"Mask之卵", "201811102"},
            {"≮Mask≯之卵", "201811103"},
            {"☆残酷☆之卵", "2018121201"},
            {"≮☆残酷☆≯之卵", "2018121202"},
            {"雅蠛蝶之卵", "2019022602"},
            {"北冥鸽之卵", "2019022603"},
            {"★虎妞宝宝★之卵", "2019030114"},
            {"灵花妖之卵", "2022021804"},
            {"五彩梦蝶之卵", "2024030810"},
        };

        private static int _times, _mainlv, _vicecc, _vicelv, _yzx;
        private static double _buff;
        private static string _mainxx, _vice, _prop1, _prop2, _jhprop;
        private static long _exp, _exp2/*当巫族时，存放涅槃兽经验*/, _jb;
        private static int _run = 0;

        private void PlayerHelper_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (AM_start.Text == "停止")
            {
                AM_Fight = false;
                AutoMapTimer.Enabled = false;
                AM_start.Text = "开始";
                AM_start.Enabled = false;
                AM_map.Enabled = true;
                AutoMapTimer.Stop();
                log("正在停止自动战斗……");
                //测试提交
            }

            if (_run == 1)
            {
                _autonp.Stop();
            }

            PetTable.Clear();
            TaskTable.Clear();
            TaskTable1.Clear();
            GameForm.ShowWebBrowser();
            Form1.isHelper = false;
        }

        internal static string AutoNirvana(PetInfo main, string vice, int mainlv, int vicelv, int times, string prop1,
            string prop2 = null, string jhprop = null)
        {
            //这里开始涅槃
            //检查参数是否合法
            //检查次数、金币、道具是否满足要求
            UserInfo user = new DataProcess().ReadUserInfo();
            if (Convert.ToInt32(user.AutoTime) < times)
            {
                return Error + "自动涅槃次数不足";
            }

            long mainexp = Convert.ToInt64(new DataProcess().GetSumExp(mainlv - 1));
            long viceexp = Convert.ToInt64(new DataProcess().GetSumExp(vicelv - 1));
            long npsexp = Convert.ToInt64(new DataProcess().GetSumExp(59));
            long sum = main.五行 != "巫"?mainexp + viceexp + npsexp :mainexp + viceexp;
            if (main.五行 != "巫" && (Convert.ToInt64(user.AutoExp) < times * sum))
            {
                return Error + "自动涅槃经验不足[在商店出售经验道具获得]";
            }
            if (main.五行 == "巫" && (Convert.ToInt64(user.AutoExp2) < times * sum))
            {
                return Error + "巫族自动涅槃经验不足[在商店出售经验道具获得]";
            }
            if(main.五行 == "巫" && (Convert.ToInt64(user.AutoExp)< npsexp))
            {
                return Error + "涅槃兽经验不足[在商店出售经验道具获得]";
            }
            long jb = NumEncrypt.五十万();
            PropInfo p1 = new DataProcess().GetAP_ID(prop1);
            if (p1 == null || Convert.ToInt32(p1.道具数量) < times)
            {
                return Error +  "涅槃道具1不足";
            }
            PropInfo p2 = new DataProcess().GetAP_ID(prop2);
            if (!string.IsNullOrEmpty(prop2))
            {
                
                int tmp;
                if (prop2 == prop1)
                {
                    tmp = times * 2;
                }
                else
                {
                    tmp = times;
                }

                if (p2 == null || Convert.ToInt32(p2.道具数量) < tmp)
                {
                    return Error + "涅槃道具2不足";
                }
            }

            PropInfo p3 = new DataProcess().GetAP_ID(vice);
            if (p3 == null || Convert.ToInt32(p3.道具数量) < times)
            {
                return Error + "副宠道具不足";
            }

            PropInfo p4 = new DataProcess().GetAP_ID("20");
            if (p4 == null || Convert.ToInt32(p4.道具数量) < times)
            {
                return Error + "涅槃兽卵不足";
            }

            if (!string.IsNullOrEmpty(jhprop))
            {
                short zzxh = Convert.ToInt16(Enum.Parse(typeof(PetProcess.五行序号), main.五行));
                if (zzxh >= 8)
                {
                    if (jhprop == "2016110545" || jhprop == "2016110546")
                    {
                        return Error + "您选择的宠物不能使用此道具进化";
                    }
                }
                else if (zzxh == 6 || zzxh == 7)
                {
                    if (jhprop == "2016110512" || jhprop == "2016110513")
                    {
                        return Error + "您选择的宠物不能使用此道具进化";
                    }
                }
                else
                {
                    return Error;
                }
                


                PropInfo p5 = new DataProcess().GetAP_ID(jhprop);
                if (p5 == null || Convert.ToInt32(p5.道具数量) < times * 10)
                {
                    return Error + "进化道具不足";
                }

                jb += 10000;
            }

            //对巫进行单独的判断
            //这里的思路是前面已经判断完了基本的，这里只要判断关键的道具就行
            if (main.五行 == "巫")
            {
                string[] eatPet = { "2022021804","2022033025" , "2024030810" };
                //if (p1 == null || Convert.ToInt32(p1.道具数量) < times)
                //{
                //    return Error + "涅槃道具1不足";
                //}
                if (p1!= null && !new DataProcess().ReadPropScript(p1.道具类型ID).道具脚本.Contains("巫族涅槃"))
                {
                    return Error + "巫族宠物不能使用涅槃道具1";
                }
                if (p2 != null && !new DataProcess().ReadPropScript(p2.道具类型ID).道具脚本.Contains("巫族涅槃"))
                {
                    return Error + "巫族宠物不能使用涅槃道具2";
                }
                //判断副宠
                if( !eatPet.Contains(vice))
                {
                    return Error + "巫族宠物不能使用该副宠道具";
                }

            }
            //===
            if (Convert.ToInt64(user.金币) < jb * times)
            {
                return Error + "金币不足";
            }
            涅槃前CC = main.成长;
            double buff = 0;
            int interval = 12000-(int)(new DataProcess().gnt()*1000);//涅槃等待时间毫秒-12000    涅槃间隔 涅槃时间
           
            //是管理员的情况下涅槃时间默认为1秒
            //if (new DataProcess().getPower())
            //{
            //    interval = 1000;
            //}

            if (prop1 == "2016100403" || prop1 == null )
            {
                buff += 0.1;
                if (prop2 == "2016100403" || prop2 == null )
                {
                    interval = 40000 - (int)(new DataProcess().gnt() * 1000); ;
                }
            }
            //计算一下涅槃需要的时间
            开始时间 = DateTime.Now.ToString();
            时间 = ((interval - (int)(new DataProcess().gnt() * 1000)) * times).ToString();
            time1 = Convert.ToInt64(((TimeSpan)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0))).TotalMilliseconds);//当前时间戳
            time1 += (interval - (int)(new DataProcess().gnt() * 1000)) * times;//时间戳+涅槃次数*涅槃时间(毫秒)

            //副宠道具
            if (prop1 == "2016100402")
            {
                buff += 0.05;
            }

            if (prop1 == "2016100401")
            {
                buff += 0.4;
            }

            if (prop1 == "2022033007") //七彩蘑菇 HM 20220614
            {
                buff += 0.15;
            }

            if (prop2 == "2016100403")
            {
                buff += 0.1;
            }

            if (prop2 == "2016100402")
            {
                buff += 0.05;
            }

            if (prop2 == "2016100401")
            {
                buff += 0.4;
            }

            if (prop2 == "2022033007") //七彩蘑菇 HM 20220614
            {
                buff += 0.15;
            }

            if (prop1 == "2022030901")//凤凰羽毛
            {
                buff += 0.1;
            }
            if (prop2 == "2022030901")//凤凰羽毛
            {
                buff += 0.1;
            }

            副宠道具 = vice;
            涅槃道具1 = prop1 != null ? prop1 : "无";
            涅槃道具2 = prop2!=null ? prop2 : "无";
            int vicecc = 10;
            if (vice == "2017080712")
            {
                vicecc = 60;
            }
            else if (vice == "2018102503")
            {
                vicecc = 200;

            }
            else if (vice == "201811102")//Mask之卵
            {
                vicecc = 1111;

            }
            else if (vice == "201811103")//≮Mask≯之卵
            {
                vicecc = 11111;

            }
            else if (vice == "2018121201")//☆残酷☆之卵
            {
                vicecc = 1112;

            }
            else if (vice == "2018121202")//≮☆残酷☆≯之卵
            {
                vicecc = 11112;

            }
            else if (vice == "2019022602")
            {
                vicecc = 220;

            }
            else if (vice == "2019022603")
            {
                vicecc = 300;

            }
            else if (vice == "2019030114")
            {
                vicecc = 260;

            }
            else if (vice == "2022021804")//灵花妖
            {
                vicecc = 20;

            }
            else if (vice == "2024030810")//五彩梦蝶之卵
            {
                vicecc = 40;

            }
            else if (vice == "2022033025") //宝宝龙 HM 20220614
            {
                vicecc = 30;
            }
            _times = times;
            _mainxx = main.宠物序号;
            _mainlv = mainlv;
            _vice = vice;
            _vicecc = vicecc;
            _vicelv = vicelv;
            _buff = buff;
            _prop1 = prop1;
            _prop2 = prop2;
            _exp = sum;
            if (main.五行 == "巫")
            {
                _exp = sum;
                _exp2 = npsexp;
            }
            _jhprop = jhprop;
            _yzx = 1;
            _jb =jb;
            SetTimer(interval);
            _run = 1;
            //检查完毕 开始执行
            PetTable.Clear();
            Form1.isHelper = true;
            return "正在自动涅槃中……";
        }

        private static void SetTimer(int interval)
        {
            _autonp = new System.Timers.Timer(interval);
            _autonp.Elapsed += OnTimedEvent1;
            _autonp.AutoReset = true;
            _autonp.Enabled = true;
        }

        private delegate void ChangeLabel9Text(string text);
        private delegate void ChangeButton1Text(string text);
        private delegate void CloseHelper();

        private void SetLabel9Text(string text)
        {
            if (InvokeRequired)
            {
                ChangeLabel9Text changeLabel9Text = SetLabel9Text;
                Invoke(changeLabel9Text, text);
            }
            else
            {
                label9.Text = text;
            }
        }

        private void SetButton1Text(string text)
        {
            if (InvokeRequired)
            {
                ChangeButton1Text changeButton1Text = SetButton1Text;
                Invoke(changeButton1Text, text);
            }
            else
            {
                button1.Text = text;
            }
        }

        private void ClosePlayerHelper()
        {
            if (InvokeRequired)
            {
                CloseHelper closeHelper = ClosePlayerHelper;
                Invoke(closeHelper);
            }
            else
            {
                Helper.Close();
            }
        }

        private void mainComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private static void OnTimedEvent1(object source, ElapsedEventArgs e)//自动涅槃函数
        {
            if (_run == 1)
            {
                if (_yzx <= _times)
                {
                    new DataProcess().judgeBad();
                    UserInfo user = new DataProcess().ReadUserInfo();
                    var pet = new DataProcess().ReadAppointedPet(_mainxx);
                    if(pet.五行 == "巫")
                    {
                        user.AutoExp = (Convert.ToInt64(user.AutoExp) - _exp2).ToString();
                        user.AutoExp2 = (Convert.ToInt64(user.AutoExp2) - _exp).ToString();
                        消耗经验1 += _exp2;//涅槃兽
                        消耗经验2 += _exp;//巫族宠物
                        
                    }
                    if (pet.五行 != "巫")
                    {
                        user.AutoExp = (Convert.ToInt64(user.AutoExp) - _exp).ToString();
                        消耗经验1 += _exp;
                    }
                    涅槃次数++;
                    
                    user.AutoTime = (Convert.ToInt32(user.AutoTime) - 1).ToString();
                    user.金币 = (Convert.ToInt64(user.金币) - _jb).ToString();
                    PetProcess.AutoNirvana(_mainxx, _mainlv, _vice, _vicecc, _vicelv, _buff, _prop1, _prop2, _jhprop);
                    new DataProcess().SaveUserDataFile(user);
                    
                    
                    Helper.SetLabel9Text($"正在执行第{_yzx}次连涅。");
                    _yzx += 1;
                    if (DataProcess.BanList.Contains("AE243838"))
                    {
                        upInfo();
                        _run = 0;
                        _autonp.Stop();
                        //MessageBox.Show("连捏已结束！");
                        Helper.SetButton1Text("退出连涅");
                        PetTable.Clear();
                        GameForm.ShowWebBrowser();
                        GameForm.showInfo("已临时关闭自动涅槃，请耐心等待开启!");
                        GameForm.showInfo($"涅槃结束。<br>累计涅槃：{涅槃次数}次<br>累计增加成长：{累计CC}" +
                        $"<br>平均增加成长：{平均CC}<br>消耗普通涅槃经验{GetExp(消耗经验1)}" +
                        $"<br>消耗巫族涅槃经验：{GetExp(消耗经验2)}" +
                        $"<br>[详情]平均增加成长（涅槃）：{平均CC_涅槃 / 涅槃次数}" +
                        $"<br>[详情]平均增加成长（进化10次）：{平均CC_进化 / 涅槃次数}");
                        Helper.ClosePlayerHelper();
                    }
                }
                else
                {
                    upInfo();
                    _run = 0;
                    _autonp.Stop();
                    //MessageBox.Show("连捏已结束！");
                    Helper.SetButton1Text("退出连涅");
                    PetTable.Clear();
                    GameForm.ShowWebBrowser();
                    GameForm.showInfo($"涅槃结束。<br>累计涅槃：{涅槃次数}次<br>累计增加成长：{累计CC}" +
                        $"<br>平均增加成长：{平均CC}<br>消耗普通涅槃经验{GetExp(消耗经验1)}" +
                        $"<br>消耗巫族涅槃经验：{GetExp(消耗经验2)}"+
                        $"<br>[详情]平均增加成长（涅槃）：{平均CC_涅槃 / 涅槃次数}" +
                        $"<br>[详情]平均增加成长（进化10次）：{平均CC_进化 / 涅槃次数}");
                    
                    Helper.ClosePlayerHelper();
                }
            }
            //Thread thread = new Thread(delegate ()
            //{
                
            //});
            //thread.Start();
            
        }

        private void RefreshPetTable()
        {
            List<PetInfo> pets = new DataProcess().ReadPlayerPetList();
            List<string> petname = new List<string>();
            int i = 1;
            foreach (PetInfo pet in pets)
            {
                string cc = Math.Round(Convert.ToDouble(pet.成长), 2).ToString(CultureInfo.InvariantCulture);
                if (pet.宠物名字.Contains("涅槃") || Convert.ToInt16(Enum.Parse(typeof(PetProcess.五行序号), pet.五行)) <= 5)
                {
                    continue;
                }

                petname.Add(i + ":" + pet.宠物名字 + " - " + cc + "cc");
                PetTable.Add(i.ToString(), pet);
                i += 1;
            }

            mainComboBox.DataSource = petname;
        }
        private Dictionary<string, string> 地图列表 = new Dictionary<string, string>
        {
            {"新手基地", "1"},
            {"妖精森林", "2"},
            {"潮汐海崖", "3"},
            {"巨石山脉", "4"},
            {"黄金陵", "5"},
            {"炽热沙滩", "6"},
            {"尤玛火山", "7"},
            {"死亡沙漠", "8"},
            {"海市蜃楼", "9"},
            {"冰滩", "10"},
            {"海底世界", "11"},
            {"圣诞小屋", "12"},
            {"石阵", "13"},
            {"平原", "14"},
            {"绿荫林", "15"},
            {"五指石印", "16"},
            {"鬼屋", "17"},
            {"天空之城", "18"},
            {"天之路", "19"},
            {"危之路", "20"},
            {"幽冥之镜", "21"},
            {"异界深渊", "22"},
            {"时空冰岛", "23"},
            {"圣兽云殿", "102"},
            {"埋骨之地", "201"},
            {"孢子林", "202"},
            {"迷雾森林", "203"},
            {"汐愿之海", "205"},
            {"巨石荒野", "206"},
            {"伊苏王的神墓", "FB9001"},
            {"火龙王的宫殿", "FB9002"},
            {"史芬克斯密穴", "FB9003"},
            {"玲珑城", "FB9004"},
            {"BOSS集中营", "FB9010"},
            {"楼兰古城的遗迹", "FB9011"},
            {"赫拉神殿", "201901"},
            {"阿尔提密林", "FB9012"},
            {"幻魔之境", "FB204"},
            {"地狱之门", null},
            {"通天塔", null}
        };
        //彩蛋
        string[] zzTip = { "九重天上闻仙乐，万宝床边侍至尊", "为谁生死，为谁轻言，一阙幽梦断何年", "惜芳却覆黄罗帕，护取花王待至尊", "生缚凶魁献至尊，不使朝廷乏名将", "阖棺谩说荣枯定，青骨犹当履至尊" };
        string[] xcTip = { "稳看腾踏上星辰", "草木摇杀气，星辰无光彩", "惜芳却覆黄罗帕，护取花王待至尊","生缚凶魁献至尊，不使朝廷乏名将", "星辰、直上无声,缓蹑素云归晚", };

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tabControl1.SelectedTab.Text != "自动战斗")
            {
                Height = 339;
                tabControl1.Height = 302;
            }
            else {
                Height = 500;
                tabControl1.Height = 461;
            }
        }

        
        Dictionary<String, String> skills = new Dictionary<string, string>();

        private void AM_map_SelectedIndexChanged(object sender, EventArgs e)
        {
            AM_looplabel.Visible = (地图列表[AM_map.Text] == null || 地图列表[AM_map.Text].Contains("FB"));
            AM_fornum.Visible = (地图列表[AM_map.Text] == null || 地图列表[AM_map.Text].Contains("FB"));
            linkLabel1.Visible = (地图列表[AM_map.Text] == null || 地图列表[AM_map.Text].Contains("FB"));
            if (AM_map.Text == "地狱之门") MessageBox.Show("层数需要填你大层数*10。\r\n比如我要重复1000层，就要输入10000。");
            if (AM_map.Text == "通天塔") MessageBox.Show("该地图可能存在不稳定的掉落，请谨慎选择，坚持使用存在问题不会有补偿。");
            if (地图列表[AM_map.Text] != null)
            {
                if (地图列表[AM_map.Text].Contains("FB"))
                {
                    var info = new DataProcess().GetAMML(地图列表[AM_map.Text].Replace("FB", ""));
                    AM_fornum.Text = info.Count.ToString();
                }
                else
                {
                    AM_fornum.Text = "0";
                }
            }
            else
            {
                AM_fornum.Text = "0";
            }
        }
        public void log(String text, bool hideTime=false) {
            try
            {
                var lines = AM_info.Text.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);
                if (lines.Length >= 1100)
                {
                    string tempText = AM_info.Text;
                    for (int i = 0; i < AM_info.Lines.Length - 1000; i++)
                        tempText = tempText.Remove(0, tempText.IndexOf('\r') + 1);
                    AM_info.Text = tempText;
                }
                if (!hideTime) AM_info.AppendText(string.Format("[{0}]{1}\r\n", DateTime.Now.ToString("HH:mm:ss.fff"), text));
                else AM_info.AppendText(string.Format("{0}\r\n", text));
                AM_info.SelectionStart = AM_info.Text.Length;
            }
            catch (Exception ex) { }
        }
        string skillID = "1";
        bool AM_Fight = false;
        //Thread fight = new Thread(new ThreadStart(AutoMapFIght()));//创建线程
        private void AM_start_Click(object sender, EventArgs e)
        {
            if (autoPet) {
                log("无法开始，请先停止自动涅槃！");
                return;
            }
            if (autoTask)
            {
                log("无法开始，请先停止自动任务！");
                return;
            }
            if (AM_skill.Text != "") {
                skillID = skills[AM_skill.Text];
            }
            if (check_DXC.Checked)
            {

            }
            else
            {
                if (AM_start.Text == "开始")
                {
                    AM_Fight = true;
                    Fight.AutoHell = false;
                    Fight.AutoMap = false;
                    Fight.AutoTT = false;
                    log("开始自动战斗……");
                    AutoMapTimer.Enabled = true;
                    AM_start.Text = "停止";
                    AM_map.Enabled = false;
                    proplog = new Dictionary<string, int>();
                    battleNum = 0;
                    loopNum = 0;
                    A_exp = 0;
                    A_money = 0;
                    A_yb = 0;
                    //测试模式
                    if(new DataProcess().getPower() && Program.getDebug() && checkBox_FIGHT_DEBUG.Checked)
                    {
                        battleMax = int.Parse(textBox_BattleNum.Text);
                    }
                    updateAutoBattleInfo();
                }
                else
                {
                    AM_Fight = false;
                    AutoMapTimer.Enabled = false;
                    AM_start.Text = "开始";
                    AM_start.Enabled = false;
                    AM_map.Enabled = true;
                    AutoMapTimer.Stop();
                    log("正在停止自动战斗……");
                }
            }
            
           
            
        }
        public void updateAutoBattleInfo() {
            AM_battlenum.Text = "战斗次数：" + battleNum;
            AM_loopnum.Text = "循环次数：" + loopNum;
            AM_BOSS.Text = "遭遇BOSS：" + bossNum;
            label17.Text = "有掉落：" + propNum;
            label18.Text = "无掉落：" + propNumNull;
            AM_money.Text = "金币：" + A_money;
            AM_yb.Text = "元宝：" + A_yb;
            AM_EXP.Text = "经验：" + A_exp;
            AM_proplist.DataSource = (from v in proplog
                                         select new
                                         {
                                             key = v.Key,
                                             value = v.Value
                                         }).OrderByDescending(C=>C.value).ToArray();

        }
        public void stopMap() {
            AutoMapTimer.Enabled = false;
            AM_start.Text = "开始";
            AutoMapTimer.Stop();
            AM_map.Enabled = true;
            AM_start.Enabled = true;
           
        }
        double upBattleTime = 0;//上次进入战斗的时间
        Dictionary<String, int> proplog = new Dictionary<string, int>();//统计道具掉落情况
        int battleNum = 0;//战斗次数
        int loopNum = 0;//使用钥匙的次数（即循环次数）
        int bossNum = 0;//遭遇BOSS的次数
        int propNum = 0;//掉落了多少道具
        int propNumNull = 0;//有多少次没掉落道具
        long A_yb = 0;
        long A_exp = 0;
        long A_money = 0;
        //测试
        int battleMax = 0;
        private void AutoMapTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                //因为战斗时间不确定，为了防止重复触发，进入时钟周期就先关闭时钟
                AutoMapTimer.Enabled = false;
                string map = 地图列表[AM_map.Text];
                bool FB = false;
                if (map != null)
                {
                    FB = map.Contains("FB");
                    map = map.Replace("FB", "");

                }
                #region 战斗前期模拟
                new DataProcess().judgeBad();
                bool tc = false;//退出
                for (var i = 0; i < DataProcess.BanList.Length; i++)
                {
                    string b = DataProcess.BanList[i];

                    if (b.Equals(map) && !new DataProcess().getPower())//在线控制地图
                    {
                        log("地图已经关闭，暂时无法进入！");
                        stopMap();

                        tc = true;
                        break;
                    }
                }
                if (tc) { stopMap(); return; };
                double thisTime = new Tools.GetTime().GetSystemTS();
                if (!Program.getDebug() &&thisTime - upBattleTime < 2000.00)
                {
                    log("战斗过快，2秒后重试……");
                    AutoMapTimer.Enabled = true;
                    AutoMapTimer.Interval = 2000;
                    return;
                }
                List<MonsterInfo> 怪物列表 = null;
                if (map != null) 怪物列表 = new DataProcess().GetAMML(map);
                else 怪物列表 = new DataProcess().GetAMML(AM_map.Text);
                if (map != null && 怪物列表.Count < 1)
                {
                    log("怪物逃跑了，2秒后重试……");
                    AutoMapTimer.Enabled = true;
                    AutoMapTimer.Interval = 2000;
                    return;
                }
                UserInfo user = new DataProcess().ReadUserInfo();
                PetInfo 怪物 = new PetInfo();

                PetInfo pet = new DataProcess().ReadAppointedPet(user.主宠物);
                if (AM_map.Text == "地狱之门")
                {
                    long 进度 = string.IsNullOrEmpty(user.地狱层数) ? 1 : Convert.ToInt64(user.地狱层数);
                    long 层数 = Convert.ToInt64(Math.Ceiling(进度 / 10.0));
                    log("地狱之门当前层数:" + 层数.ToString() + "(" + (进度 % 10 == 0 ? "10" : (进度 % 10).ToString()) + "/10)");
                    //setBJ
                    怪物 = new DataProcess().GetHellMonster();
                    怪物.地狱之门 = true;
                }
                else if (AM_map.Text == "通天塔")
                {
                    log("通天塔当前层数:" + (string.IsNullOrEmpty(user.TTT) ? 1 : Convert.ToInt32(user.TTT)));
                    //setBJ
                    if (user.TTT != null && Convert.ToInt32(user.TTT) > 500)
                    {
                        stopMap();
                        log("通天塔最高只能五百层！");
                        return;
                    }
                    怪物 = new DataProcess().GetTtMonster();
                    怪物.TTT = true;
                }
                else
                {
                    {
                        string ico = null;
                        try
                        {
                            ico = new DataProcess().ReadMapInfo(map).ICO;
                        }
                        catch (Exception)
                        {
                            new DataProcess().ThrowError(2);
                        }

                        if (ico == null)
                        {
                            ico = "1.jpg";
                        }
                        var mapInfo = new DataProcess().ReadMapInfo(map);
                        if (mapInfo.限制钥匙)
                        {
                            if (user.openMaps == null || !user.openMaps.Contains(map))
                            {
                                log("请开启地图后再来，该地图需要解锁后才能挑战！");
                                stopMap();
                                return;
                            }
                        }
                        if (Convert.ToDouble(pet.成长) < mapInfo.限制成长)
                        {
                            log($"挑战该地图需要{mapInfo.限制成长}成长，您的成长不足，无法进入");
                            stopMap();
                            return;
                        }
                        if (Convert.ToDouble(pet.等级) < mapInfo.限制等级)
                        {
                            log($"挑战该地图需要{mapInfo.限制等级}级，您的等级不足，无法进入");
                            stopMap();
                            return;
                        }
                        //限制巫族地图只能指定宠物进入
                        if (mapInfo.限制五行 == "巫")
                        {
                            if (pet.形象 != "681")
                            {
                                log($"挑战该地图只能由★猪苗苗★进入");
                                stopMap();
                                return;
                            }
                        }
                        //限制定制宠物进入巫族地图
                        if (mapInfo.限制五行 == "巫" && (pet.形象 == "506" || pet.形象 == "507" || pet.形象 == "508" || pet.形象 == "514"))
                        {
                            log($"挑战该地图定制宠物无法进入无法进入");
                            stopMap();
                            return;
                        }
                        if (mapInfo.限制五行 != null && mapInfo.限制五行 != "" && !mapInfo.限制五行.Split('|').Contains(pet.五行))
                        {
                            log($"挑战该地图需要五行为{mapInfo.限制五行}，您无法进入");
                            stopMap();
                            return;
                        }
                        if (mapInfo.Type.Equals("1"))
                        {
                            FBROP 层数 = new DataProcess().GetFBROP(map);
                            if (层数 == null)
                            {
                                层数 = new FBROP { id = map, num = "0" };
                                log("您是第一次打该副本,免费赠送一次机会.");
                            }

                            if (层数.num == null || Convert.ToInt32(层数.num) == -10)
                            {
                               
                                log("副本未开启,请在使用钥匙后再来(如果你需要使用自动功能，请自行使用钥匙后再开始)");
                                stopMap();
                                return;
                            }

                            Fight.FBMap = true;
                            //战斗处理.PK = false;
                            log("当前层数:" + (Convert.ToInt32(层数.num) + 1) + ",本副本一共有" + 怪物列表.Count + "层.");
                            怪物 = new DataProcess().ChooseAM(怪物列表, Convert.ToInt32(层数.num));
                            //通天塔怪物属性
                            if (怪物.状态 != "固定属性")
                            {
                                怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 5).ToString(CultureInfo.InvariantCulture);
                                怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 3).ToString(CultureInfo.InvariantCulture);
                                怪物.最大生命 = Convert.ToInt64(Convert.ToDouble(怪物.最大生命) * 2).ToString(CultureInfo.InvariantCulture);
                                怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 2).ToString(CultureInfo.InvariantCulture);
                            }
                        }
                        else
                        {
                            Fight.FBMap = false;//地图难度
                                                //战斗处理.PK = false;

                            if (map != null)
                            {
                                if (user.NextBattle != null && user.NextBattle != "")
                                {
                                    foreach (MonsterInfo 信息 in 怪物列表)
                                    {
                                        if (信息.序号 == user.NextBattle)
                                        {
                                            怪物列表 = new List<MonsterInfo>();
                                            怪物列表.Add(信息);
                                            user.NextBattle = "";
                                            new DataProcess().SaveUserDataFile(user);
                                            break;
                                        }
                                    }
                                }
                            }
                            if (Convert.ToInt32(user.刷怪数) < NumEncrypt.二百())
                            {
                                怪物 = new DataProcess().ChooseRM(怪物列表, 0);
                            }
                            else if (Convert.ToInt32(user.刷怪数) >= NumEncrypt.二百() && Convert.ToInt32(user.刷怪数) <= (9112 - new DataProcess().gbs()) ||
                                     Convert.ToInt32(user.刷怪数) > ((9112 - new DataProcess().gbs()) - new DataProcess().gbs()) && DataProcess.Noboss)
                            {
                                int boss = DataProcess.RandomGenerator.Next(0, 1000);
                                if (boss == NumEncrypt.五百())
                                {
                                    怪物 = new DataProcess().ChooseRM(怪物列表, 1);
                                    //recv("[Boss](pk)|" + 怪物.宠物名字);
                                    if (怪物.宠物名字.Contains('§'))
                                    {
                                        log("恭喜玩家幸运地遇到BOSS" + 怪物.宠物名字 + "，请击败BOSS以获得奖励!");
                                        bossNum++;
                                        if (!DataProcess.notMap.Contains(map))
                                        {
                                            怪物.最大生命 = Convert.ToInt64(Convert.ToDouble(怪物.最大生命) * 20).ToString(CultureInfo.InvariantCulture);
                                            怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 20).ToString(CultureInfo.InvariantCulture);
                                            怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 3).ToString(CultureInfo.InvariantCulture);
                                            怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 5).ToString(CultureInfo.InvariantCulture);
                                        }
                                       
                                    }
                                }
                                else
                                {
                                    怪物 = new DataProcess().ChooseRM(怪物列表, 0);
                                    if (怪物.形象.Equals("106"))
                                    {
                                        怪物.最大生命 = Convert.ToInt64(Convert.ToDouble(怪物.最大生命) * 20).ToString(CultureInfo.InvariantCulture);
                                        怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 20).ToString(CultureInfo.InvariantCulture);
                                    }

                                    if (怪物.形象.Equals("98"))
                                    {
                                        怪物.最大生命 = 6.ToString();
                                        怪物.生命 = 6.ToString();
                                    }
                                }
                            }
                            else if (Convert.ToInt32(user.刷怪数) > (9112 - new DataProcess().gbs()) && !DataProcess.Noboss)
                            {
                                怪物 = new DataProcess().ChooseRM(怪物列表, 1);
                                //recv("[Boss](pk)|" + 怪物.宠物名字);
                                if (怪物.宠物名字.Contains('§'))
                                {
                                    log($"由于刷怪数超过{(9112 - new DataProcess().gbs())},您已进入必遇BOSS模式!");
                                    log("恭喜玩家幸运地遇到BOSS" + 怪物.宠物名字 + "，请击败boss获得奖励!");
                                    bossNum++;
                                    if (!DataProcess.notMap.Contains(map))
                                    {
                                        怪物.最大生命 = Convert.ToInt64(Convert.ToDouble(怪物.最大生命) * 20).ToString(CultureInfo.InvariantCulture);
                                        怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 20).ToString(CultureInfo.InvariantCulture);
                                        怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 3).ToString(CultureInfo.InvariantCulture);
                                        怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 5).ToString(CultureInfo.InvariantCulture);
                                    }
                                }
                            }

                            //地图加强/怪物加强

                            if (怪物.形象.Equals("106"))
                            {
                                怪物.最大生命 = Convert.ToInt64(Convert.ToDouble(怪物.最大生命) * 20).ToString(CultureInfo.InvariantCulture);
                                怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 20).ToString(CultureInfo.InvariantCulture);
                            }

                            if (怪物.形象.Equals("98") && map == "201901")
                            {
                                怪物.最大生命 = 6.ToString();
                                怪物.生命 = 6.ToString();

                            }
                            if (怪物.形象.Equals("20190102"))
                            {
                                怪物.最大生命 = 400.ToString();
                                怪物.生命 = 400.ToString();
                            }

                            if (怪物.宠物名字.Contains("魔化★"))//幽冥之镜/异界深渊
                            {
                                怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 5).ToString(CultureInfo.InvariantCulture);
                                怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 3).ToString(CultureInfo.InvariantCulture);
                                怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 24).ToString(CultureInfo.InvariantCulture);
                                怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 24).ToString(CultureInfo.InvariantCulture);
                                怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 3).ToString(CultureInfo.InvariantCulture);
                                怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 3).ToString(CultureInfo.InvariantCulture);

                                if (map == "22")//异界深渊
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 4).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 2).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 20).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 2).ToString(CultureInfo.InvariantCulture);
                                }

                                if (怪物.宠物名字.Contains("魔化★暗夜女神"))
                                {
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) - Convert.ToDouble(怪物.最大生命) * 0.38).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = (Convert.ToDouble(怪物.生命) - Convert.ToDouble(怪物.生命) * 0.38).ToString(CultureInfo.InvariantCulture);
                                }
                            }
                            if (怪物.形象.Equals("98"))
                            {
                                怪物.最大生命 = 6.ToString();
                                怪物.生命 = 6.ToString();

                            }
                            if (怪物.宠物名字.Equals("虚弱-"))
                            {
                                怪物.最大生命 = 6.ToString();
                                怪物.生命 = 6.ToString();

                            }
                            if (map == "201901")//赫拉神殿
                            {
                                怪物.最大生命 = 3.ToString();
                                怪物.生命 = 3.ToString();
                                if (怪物.宠物名字.Contains('§'))//BOSS
                                {
                                    怪物.最大生命 = 99.ToString();
                                    怪物.生命 = 99.ToString();
                                }
                                // UserInfo user = new DataProcess().ReadUserInfo();
                                if (user.至尊VIP || user.星辰VIP)//赫拉神殿-特权公告//删除提示，打一次提示一次
                                {
                                    //发送红色公告($"已激活特权,每回合对怪物额外照成1点伤害,对BOSS额外照成{(user.星辰VIP ? (8) :user.至尊VIP ? (5) : 1)}点伤害。");
                                    //怪物.生命 = 90.ToString();
                                }
                                //if (怪物.形象.Equals("20190110"))//彩灯仙子
                                //{
                                //    怪物.最大生命 = 88.ToString();
                                //    怪物.生命 = 88.ToString();
                                //}
                                //if (怪物.形象.Equals("20190111"))//彩灯仙子
                                //{
                                //    怪物.最大生命 = 88.ToString();
                                //    怪物.生命 = 88.ToString(); 
                                //}
                            }

                            //新新大陆怪物属性 205地图去掉基础属性
                            if (map == "201" || map == "203" || map == "204" || map == "206")
                            {
                                //
                                //怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 30).ToString(CultureInfo.InvariantCulture);
                                if (怪物.宠物名字.Contains('§'))//BOSS
                                {
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 1.8).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 1.8).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 1.2).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 1.5).ToString(CultureInfo.InvariantCulture);
                                }
                                else
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 1.2).ToString(CultureInfo.InvariantCulture);
                                }

                            }

                            if (map == "202")//孢子林
                            {
                                //
                                //怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 30).ToString(CultureInfo.InvariantCulture);
                                if (怪物.宠物名字.Contains('§'))//BOSS
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 1).ToString(CultureInfo.InvariantCulture);
                                }
                                else
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 1).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 1).ToString(CultureInfo.InvariantCulture);
                                }

                            }

                            if (map == "203")//迷雾森林
                            {
                                //
                                //怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 30).ToString(CultureInfo.InvariantCulture);
                                if (怪物.宠物名字.Contains('§'))//BOSS
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 2.4).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 6).ToString(CultureInfo.InvariantCulture);
                                }
                                else
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 6).ToString(CultureInfo.InvariantCulture);
                                }
                            }

                            if (map == "206")//巨石荒野
                            {
                                //
                                //怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 30).ToString(CultureInfo.InvariantCulture);
                                if (怪物.宠物名字.Contains('§'))//BOSS
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 14).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 6).ToString(CultureInfo.InvariantCulture);
                                }
                                else
                                {
                                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 6).ToString(CultureInfo.InvariantCulture);
                                    怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 6).ToString(CultureInfo.InvariantCulture);
                                }
                            }

                            //============

                            if (map == "23")//冰岛怪物属性
                            {
                                怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) * 90).ToString(CultureInfo.InvariantCulture);
                                怪物.命中 = Convert.ToInt64(Convert.ToDouble(怪物.命中) * 30).ToString(CultureInfo.InvariantCulture);
                                怪物.最大生命 = (Convert.ToDouble(怪物.最大生命) * 530).ToString(CultureInfo.InvariantCulture);
                                怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 530).ToString(CultureInfo.InvariantCulture);
                                怪物.防御 = Convert.ToInt64(Convert.ToDouble(怪物.防御) * 800).ToString(CultureInfo.InvariantCulture);
                                怪物.速度 = Convert.ToInt64(Convert.ToDouble(怪物.速度) * 14).ToString(CultureInfo.InvariantCulture);
                                //怪物.攻击 = 1.ToString();
                                //怪物.最大生命 = 120000000000000.ToString();
                                //怪物.生命 = 120000000000000.ToString();
                            }
                        }
                    }
                }
                //副本属性在下面改
                if (map == "9011" || map == "9010" || map == "9012")//楼兰古城的遗迹|BOSS集中营|阿尔提密林
                {
                    怪物.最大生命 = Convert.ToInt64(Convert.ToDouble(怪物.最大生命) * 15).ToString(CultureInfo.InvariantCulture);
                    怪物.生命 = Convert.ToInt64(Convert.ToDouble(怪物.生命) * 15).ToString(CultureInfo.InvariantCulture);
                    怪物.闪避 = Convert.ToInt64(Convert.ToDouble(怪物.闪避) * 1.3).ToString(CultureInfo.InvariantCulture);
                    //怪物.防御 = (Convert.ToDouble(怪物.防御) * 30000).ToString(CultureInfo.InvariantCulture);
                    怪物.攻击 = Convert.ToInt64(Convert.ToDouble(怪物.攻击) - Convert.ToDouble(怪物.攻击) * 0.4).ToString(CultureInfo.InvariantCulture);
                }
                Fight.Hell = AM_map.Text == "地狱之门";
                Fight.TT = AM_map.Text == "通天塔";
                //Fight.WorldBOSS = WorldBOSS;
                ConvertJson cj = new ConvertJson();

                if (pet == null)
                {
                    MessageBox.Show(Res.RM.GetString("存档损坏提示"));
                    return;
                }

                pet = PetCalc.CalcFightPetInfo(pet);

                upBattleTime = new Tools.GetTime().GetSystemTS();

                if (Convert.ToInt32(user.自动战斗次数) <= 0)
                {
                    log("自动战斗次数不够！无法自动！");
                    stopMap();
                    return;
                }
                user.自动战斗次数 = (Convert.ToInt32(user.自动战斗次数) - 1).ToString();
                new DataProcess().SaveUserDataFile(user);
                string mapc = map;
                if (map == null) mapc = AM_map.Text;

                if (!new DataProcess().fight(怪物, pet, mapc))
                {
                    log("自动战斗失败（未知原因），2秒后重试！");
                    log("怪物逃跑了，2秒后重试……");
                    AutoMapTimer.Enabled = true;
                    AutoMapTimer.Interval = 2000;
                    return;
                }
                #endregion
                log("成功进入战斗，战斗信息：");
                battleNum++;
                

                if (Fight.怪物 != null)
                {
                    log(string.Format("{0}({1}/{2})", Fight.怪物.宠物名字, Fight.怪物.生命, Fight.怪物.最大生命));
                    log(string.Format("{0}({1}/{2})", Fight.宠物.宠物名字, Fight.宠物.生命, Fight.宠物.最大生命));
                }
                log("正在发招……");
                if (checkBox_FIGHTFAST.Checked && Program.getDebug()) Sleep(10);//战斗间隔 - 1200
                else Sleep(3000);

                if (AM_start.Text == "开始")
                {
                    AM_start.Enabled = true;
                    return;
                }
                
                FightResult 结果 = Fight.发招(skillID);
                //		advance	=1战斗结束,这里判断是否是至尊VIP/星辰VIP的收益
                //转换经验
                log("成功攻击，造成伤害：" + 结果.输出);
                log("成功攻击，受到伤害：" + 结果.受到伤害);
                if (Fight.怪物 != null)
                {
                    log(string.Format("{0}({1}/{2})", Fight.怪物.宠物名字, Fight.怪物.生命, Fight.怪物.最大生命));
                    log(string.Format("{0}({1}/{2})", Fight.宠物.宠物名字, Fight.宠物.生命, Fight.宠物.最大生命));
                }

                int 连续无伤害 = 0;

                while (结果.战斗是否结束 == 0)
                {

                    if (checkBox_FIGHTFAST.Checked && Program.getDebug()) Sleep(10);//战斗间隔 - 1200
                    else Sleep(3000);
                    if (AM_start.Text == "开始")
                    {
                        AM_start.Enabled = true;
                        return;
                    }
                    结果 = Fight.发招(skillID);
                    log("成功攻击，造成伤害：" + 结果.输出);
                    log("成功攻击，受到伤害：" + 结果.受到伤害);
                    if(结果.输出 == 0)
                    {
                        连续无伤害++;
                    }
                    if (连续无伤害 >= 3)
                    {
                        log("战斗异常，已重新开始战斗。");
                        break;
                    }
                    if (Fight.怪物 != null)
                    {
                        log(string.Format("{0}({1}/{2})", Fight.怪物.宠物名字, Fight.怪物.生命, Fight.怪物.最大生命));
                        log(string.Format("{0}({1}/{2})", Fight.宠物.宠物名字, Fight.宠物.生命, Fight.宠物.最大生命));
                    }
                }
                //取消这个特权
                //if (结果.advance == 1 && 结果.获得经验 > 0 && new DataProcess().gjy() > 0)
                //{
                //    user = new DataProcess().ReadUserInfo();
                //    user.AutoExp = (Convert.ToInt64(user.AutoExp) + (int)(结果.获得经验 * new DataProcess().gjy())).ToString();
                //    //发送红色公告($"获得{new DataProcess().gjy() * 100}%的自动涅槃经验。获得[{(int)(结果.获得经验 * new DataProcess().gjy())}],累计[{user.AutoExp}]");
                //    new DataProcess().SaveUserDataFile(user);
                //}
                if (结果.是否死亡 == 0)
                {
                    log("战斗胜利！");
                    if (结果.获得道具 != "" && 结果.获得道具 != ";")
                    {
                        log("获得道具：" + 结果.获得道具);
                        var props = 结果.获得道具.Split('、');
                        foreach (var p in props)
                        {
                            if (!proplog.ContainsKey(p))
                            {
                                proplog.Add(p, 0);
                            }
                            proplog[p]++;
                        }
                        propNum++;
                    }
                    else
                    {
                        propNumNull++;
                    }
                    A_exp += 结果.获得经验;
                    A_money += 结果.获得金币;
                    A_yb += 结果.获得元宝;
                    log(string.Format("获得经验：{0}，获得金币：{1}，获得元宝：{2}", 结果.获得经验, 结果.获得金币, 结果.获得元宝));
                    int num;
                    bool IsNumber = int.TryParse(AM_fornum.Text, out num);
                    if (IsNumber && num > 0)
                    {
                        PropInfo key = null;
                        bool autoKey = false;
                        if (AM_map.Text.Equals("通天塔"))
                        {

                            key = new DataProcess().GetAP_ID("2018021701");
                            long tmp = Convert.ToInt64(user.TTT) + 1;
                            if (tmp > num)//这能是这里用了>=导致不打最后一只怪
                            {
                                autoKey = true;
                            }

                        }
                        else if (AM_map.Text.Equals("地狱之门"))
                        {
                            key = new DataProcess().GetAP_ID("2016101705");
                            long tmp = Convert.ToInt64(user.地狱层数) + 1;
                            if (tmp > num)//这能是这里用了>=导致不打最后一只怪
                            {
                                autoKey = true;
                            }
                        }
                        else if (FB)
                        {
                            var r = DataProcess.AutoMap.FirstOrDefault(C => C.mapID.ToString() == map);
                            if (r == null)
                            {
                                stopMap();
                                log("当前地图无法自动使用钥匙，请联系管理员添加支持！");
                                return;
                            }
                            var info = new DataProcess().GetFBROP(map);
                            int mnum = info == null || info.num == null ? 0 : Convert.ToInt32(info.num);
                            long tmp = mnum + 1;
                            if (tmp == -9 || tmp > num)//这能是这里用了>=导致不打副本最后一只怪
                            {
                                autoKey = true;
                            }
                            key = new DataProcess().GetAP_ID(r.propID);
                        }
                        else
                        {
                            AM_fornum.Text = "";
                        }
                        if (AM_fornum.Text != "" && autoKey)
                        {
                            if (key == null || Convert.ToInt32(key.道具数量) < 1)
                            {
                                log("钥匙不足，自动" + AM_map.Text + "已经结束。");
                                stopMap();
                                return;
                            }
                            else
                            {
                                new DataProcess().ReviseOrDeletePP(key, 1);
                                loopNum++;
                                log("成功消耗钥匙！重置到1层！");
                                if (AM_map.Text.Equals("地狱之门")) user.地狱层数 = "1";
                                else if (AM_map.Text.Equals("通天塔")) user.TTT = "1";
                                else new DataProcess().ChangeROP(map, "0");
                                new DataProcess().SaveUserDataFile(user);
                            }
                        }
                    }

                }
                else
                {
                    log("战斗失败！");
                }
                log("===============");
                log("1.2秒后进入下一场战斗。");
                updateAutoBattleInfo();
                //测试，打到指定数量
                if (battleNum >= battleMax && battleMax > 0)
                {
                    AM_Fight = false;
                    AutoMapTimer.Enabled = false;
                    AM_start.Text = "开始";
                    AM_start.Enabled = false;
                    AM_map.Enabled = true;
                    AutoMapTimer.Stop();
                    log("正在停止自动战斗……");
                    return;
                }
                //======
                if (checkBox_FIGHTFAST.Checked && Program.getDebug()) Sleep(10);//战斗间隔 - 1200
                else Sleep(1200);
                if (AM_start.Text == "开始")
                {
                    AM_start.Enabled = true;
                    return;
                }
                AutoMapTimer.Enabled = true;
                AutoMapTimer.Interval = 1;
            }
            catch (Exception ex) {

                log("出现了未知错误，两秒后重试！错误原因："+ex.Message+"\r\n");
                Fight.怪物 = null;
                Sleep(2000);
                if (AM_start.Text == "开始")
                {
                    AM_start.Enabled = true;
                    return;
                }
                AutoMapTimer.Enabled = true;
                AutoMapTimer.Interval = 1;
                updateAutoBattleInfo();
                return;
            }
            //成功进入战斗，开始处理
        }

        //使用道具
        public static string useProp(string 道具序号)
        {
            //Stopwatch sw = new Stopwatch();
            //sw.Start();
            PropInfo 道具 = new DataProcess().GetAP_XH(道具序号);
            if (道具 == null)
            {
                return "使用道具失败!道具不存在!";
            }
            PropConfig info = new DataProcess().ReadPropScript(道具.道具类型ID);

            if (info.道具脚本.Contains("多脚本选择"))
            {
                string[] split = info.道具脚本.Split('!');
                if (split.Length >= 2)
                {
                    Dictionary<String, String> dic = JsonConvert.DeserializeObject<Dictionary<String, String>>(split[1]);
                    List<String> select = new List<string>();
                    foreach (var d in dic)
                    {
                        select.Add(d.Key);
                    }
                    return "选择|" + string.Join(",", select);

                }
            }
            if (道具 == null)
            {
                return "使用道具失败!";
            }

            string 结果 = new DataProcess().UseProp(道具);
            return 结果;
        }
        public void Sleep(int T) {
            Thread t = new Thread(o => Thread.Sleep(T));
            t.Start(this);
            while (t.IsAlive)
            {
                //防止UI假死
                Application.DoEvents();
                Thread.Sleep(10);
            }
            if (AM_start.Text == "开始")
            {
                AM_start.Enabled = true;
                return;
            }
        }
        private void PlayerHelper_Load(object sender, EventArgs e)
        {
            RefreshPetTable();
            RefreshTaskTable1();//自带
            tabControl1_SelectedIndexChanged(null,null);
            UserInfo user = new DataProcess().ReadUserInfo();
            Random rd = new Random();
            涅槃次数 = 0;
            累计CC = 0;
            平均CC_涅槃 = 0;
            平均CC_进化 = 0;
            平均CC = 0;
            消耗经验1 = 0;
            消耗经验2 = 0;
            宠物ID = "";
            涅槃道具1 = "";
            涅槃道具2 = "";
            副宠道具 = "";
            开始时间 = "";
            时间 = "";
            涅槃前CC = "";
            涅槃后CC = "";
            if (user.星辰VIP)
            {
                this.Text = xcTip[rd.Next(zzTip.Length-1)];
                tabPage1.Text = "欲摘星辰";
                tabPage2.Text = "星辰之石";
            }
            else if (user.至尊VIP)
            {
                this.Text = zzTip[rd.Next(xcTip.Length - 1)];
                tabPage1.Text = "浴火涅槃";
                tabPage2.Text = "凤凰之羽";
            }
            else
            {
                this.Text = "玩家助手";
            }
            if (string.IsNullOrEmpty(user.TaskHelper) || !user.TaskHelper.Contains("buy"))
            {
                tabPage2.Parent = null;
            }
            if(!new DataProcess().getPower() || !Program.getDebug())
            {
                tabPage7.Parent = null;
            }
            if (new DataProcess().getPower() && Program.getDebug())
            {
                panel_FIGHT_DEBUG.Visible = true;
            }
            else
            {
                RefreshTaskTable();
            }
            if (!DataProcess.BanList.Contains("D30F1B46"))
            {
                //自动战斗地图列表加载
                foreach (var map in 地图列表)
                {
                    AM_map.Items.Add(map.Key);
                }
                //自动战斗宠物显示
                PetInfo pet = new DataProcess().ReadAppointedPet(user.主宠物);
                //tabControl1.SelectedIndex = 2;
                AM_petname.Text = "出战宠物：" + pet.宠物名字;
                //自动战斗显示宠物技能
                foreach (var skill in pet.技能显示.Split(','))
                {
                    var s = skill.Split('|');
                    //筛选技能，只显示主动技能
                    if (s.Length >= 5 && s[4] == "false")
                    {
                        AM_skill.Items.Add(s[0]);
                        if (!skills.ContainsKey(s[0])) skills.Add(s[0], s[2]);
                    }
                }
            }
            else
            {
                AM_start.Enabled = false;
                tabPage4.Parent = null;
                MessageBox.Show("自动战斗暂时不可用!");
            }
            
            
        }
        static Int64 time1=0;//涅槃需要的时间戳

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            AM_fornum.Text = "0";
        }

        private void propComBox1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void FIGHT_ATK_CheckedChanged(object sender, EventArgs e)
        {
            if(!new DataProcess().getPower() || !Program.getDebug())
            {
                FIGHT_ATK.Checked = false;
            }
        }

        private void checkBox_FIGHT_DEBUG_CheckedChanged(object sender, EventArgs e)
        {
            if (!new DataProcess().getPower() || !Program.getDebug())
            {
                checkBox_FIGHT_DEBUG.Checked = false;
            }
        }

        private void checkBox_FIGHTFAST_CheckedChanged(object sender, EventArgs e)
        {
            if(!new DataProcess().getPower() || !Program.getDebug()) checkBox_FIGHTFAST.Checked = false;
        }

        private void label19_Click(object sender, EventArgs e)
        {
            label19.Visible = false;
        }

        bool autoPet = false;//自动涅槃
        private void button1_Click(object sender, EventArgs e)
        {
            if (DataProcess.BanList.Contains("AE243838"))
            {
                label9.Text = Error + "自动涅槃暂不可用。";
                return;
            }
            if(AM_Fight)
            {
                label9.Text = Error + "请先停止自动战斗。";
                return;
            }
            if (_run == 0)
            {
                
                if (string.IsNullOrEmpty(mainComboBox.Text) || string.IsNullOrEmpty(viceComboBox.Text) ||
                    string.IsNullOrEmpty(propComBox1.Text))
                {
                    label9.Text = Error + "未设置主副宠、主副宠等级或涅槃道具1。";
                    return;
                }

                if (string.IsNullOrEmpty(mainLvTextBox.Text) || string.IsNullOrEmpty(viceLvTextBox.Text) ||
                    string.IsNullOrEmpty(timesTextBox.Text))
                {
                    label9.Text = Error + "未设置主副宠等级或连涅次数";
                    return;
                }
                else
                {
                    if (Convert.ToInt32(mainLvTextBox.Text) < 60 || Convert.ToInt32(mainLvTextBox.Text) > 130 ||
                        Convert.ToInt32(viceLvTextBox.Text) < 60 || Convert.ToInt32(viceLvTextBox.Text) > 130)
                    {
                        label9.Text = Error + "主副宠等级不能低于60且不能大于130。";
                        return;
                    }

                    if (Convert.ToInt32(timesTextBox.Text) < 1 || Convert.ToInt32(timesTextBox.Text) > 9999)
                    {
                        label9.Text = Error + "所设置的自动涅槃次数必须大于0且小于等于9999。";
                        return;
                    }
                }
                autoPet = true;
                PetInfo main = PetTable[mainComboBox.Text.Split(':')[0]];
                宠物ID = main.形象;
                string vice = ViceProp[viceComboBox.Text];
                int mainlv = Convert.ToInt32(mainLvTextBox.Text);
                int vicelv = Convert.ToInt32(viceLvTextBox.Text);
                int times = Convert.ToInt32(timesTextBox.Text);
                string prop1 = NirvanaProp[propComBox1.Text];
                string prop2 = null;
                string jhprop = null;
                string[] banPet = { "610", "609","581","578" };
                if (banPet.Contains(main.形象)|| banPet.Contains(main.五行) )
                {
                    label9.Text = "该宠物无法在玩家助手中涅槃";
                    return;
                }
                if (!string.IsNullOrEmpty(propComBox2.Text))
                {
                    prop2 = NirvanaProp[propComBox2.Text];
                }

                if (!string.IsNullOrEmpty(jhPropComBox.Text))
                {
                    jhprop = EvolutionProp[jhPropComBox.Text];
                }
                
                label9.Text = AutoNirvana(main, vice, mainlv, vicelv, times, prop1, prop2, jhprop);
                if (!label9.Text.Contains(Error))
                {
                    //Text = sjczwb(time1.ToString())+" 结束涅槃";
                    button1.Text = "停止连捏";
                }
            }
            else
            {
                _autonp.Stop();
                PetTable.Clear();
                autoPet = false;
                _run = 0;
                upInfo();
                if (涅槃次数 > 0) 
                {
                    MessageBox.Show($"累计涅槃：{涅槃次数}次\r\n累计增加成长：{累计CC}" +
                    $"\r\n平均增加成长：{平均CC}<br>消耗普通涅槃经验{GetExp(消耗经验1)}" +
                    $"\r\n消耗巫族涅槃经验：{GetExp(消耗经验2)}" +
                    $"\r\n[详情]平均增加成长（涅槃）：{平均CC_涅槃 / 涅槃次数}" +
                    $"\r\n[详情]平均增加成长（进化10次）：{平均CC_进化 / 涅槃次数}"
                    , "手动停止涅槃结果");
                }
                
                GameForm.ShowWebBrowser();
                Close();

            }
        }
        string sjczwb(string timeStamp)//时间戳转文本
        {
            if (timeStamp == "") return "无";
            DateTime dateTimeStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            long lTime = 0;
            if (timeStamp.Length<=10) lTime = long.Parse(timeStamp + "0000000");
            else lTime = long.Parse(timeStamp + "0000");
            TimeSpan toNow = new TimeSpan(lTime);
            return dateTimeStart.Add(toNow).ToString("yyyy-MM-dd HH:mm:ss");
        }
        internal void RefreshTaskTable(bool prestrain = false)
        {
            List<TaskInfo> alltask = new DataProcess().GetAllTaskAim();
            foreach (TaskInfo tasks in alltask)
            {
                if (tasks.允许重复 == "1")
                {
                    bool j1 = true;
                    bool j2 = true;
                    bool j3 = false;
                    if (!tasks.任务名.Contains("【日常】") && !tasks.任务名.Contains("【追龙】"))
                    {
                        j1 = false;
                    }

                    foreach (task taskaim in tasks.任务目标)
                    {
                        if (taskaim.Type == "VIP")
                        {
                            if (Convert.ToInt16(new DataProcess().ReadUserInfo().vip) < Convert.ToInt16(taskaim.Num))
                            {
                                j2 = false;
                            }
                        }

                        if (taskaim.Type == "击杀")
                        {
                            j3 = true;
                        }
                    }

                    if (j1 && j2 && j3)
                    {
                        if (prestrain)
                        {
                            DataProcess.Task_TaskHelper.Add(tasks);
                        }
                        else
                        {
                            TaskTable.Add(tasks.任务名, tasks.任务序号);
                            checkedListBox1.Items.Add(tasks.任务名);
                        }
                    }
                }
            }
        }

        internal void RefreshTaskTable1()
        {
            List<TaskInfo> alltask = new DataProcess().GetAllTaskAim();
            List<string> taskNameList = new List<string>();
            foreach (TaskInfo tasks in alltask)
            {
                if (tasks.允许重复 == "1")
                {
                    bool j1 = true;
                    bool j2 = true;
                    bool j3 = true;
                    if (tasks.任务名.Contains("【技能】") || tasks.任务名.Contains("【装备】") || tasks.任务名.Contains("【追龙】"))
                    {
                        j1 = false;
                        if (tasks.任务名.Contains("追龙奖励"))
                        {
                            j1 = true;
                        }
                    }
                    //UserInfo user = new DataProcess().ReadUserInfo();
                    if (new DataProcess().getPower())
                    {
                        //tabPage3.Text = "任务助手";
                        if (tasks.任务名.Contains("【追龙】") && tasks.任务名.Contains("①"))
                        {
                            j1 = true;
                        }
                    }
                    foreach (task taskaim in tasks.任务目标)
                    {
                        if (taskaim.Type == "VIP")
                        {
                            if (Convert.ToInt16(new DataProcess().ReadUserInfo().vip) < Convert.ToInt16(taskaim.Num))
                            {
                                j2 = false;
                            }
                        }

                        if (taskaim.Type == "击杀")
                        {
                            j3 = false;
                        }
                    }

                    if (j1 && j2 && j3)
                    {
                        TaskTable1.Add(tasks.任务名, tasks.任务序号);
                        taskNameList.Add(tasks.任务名);
                    }
                }
            }

            taskcomboBox1.DataSource = taskNameList;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            StringBuilder taskHelperCfg = new StringBuilder("buy", 128);
            int sum = 0;
            for (int i = 0; i < checkedListBox1.Items.Count; i++)
            {
                if (checkedListBox1.GetItemChecked(i))
                {
                    sum += 1;
                    string taskxh = TaskTable[checkedListBox1.GetItemText(checkedListBox1.Items[i])];
                    taskHelperCfg.Append("|" + taskxh);
                    new DataProcess().ReceiveTask(taskxh);
                    checkedListBox1.SetItemChecked(i, false);
                }
            }

            if (sum == 0)
            {
                MessageBox.Show("您没有选择任何任务，请重选！", Res.RM.GetString("任务助手"));
                return;
            }

            if (sum > 5)
            {
                MessageBox.Show("每次最多选择5个任务，请重选！", Res.RM.GetString("任务助手"));
                return;
            }

            UserInfo user = new DataProcess().ReadUserInfo();
            user.TaskHelper = taskHelperCfg.ToString();
            new DataProcess().SaveUserDataFile(user);
            MessageBox.Show("设置成功，现在每次战斗后会自动完成达成条件的任务并重新接取！", Res.RM.GetString("任务助手"));
        }

        private void button3_Click(object sender, EventArgs e)
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            user.TaskHelper = "buy";
            new DataProcess().SaveUserDataFile(user);
            MessageBox.Show("清空原有设定成功！", Res.RM.GetString("任务助手"));
        }
        bool autoTask = false;//自动任务状态
        private void button4_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(taskcomboBox1.Text))
            {
                MessageBox.Show("您没有选择任务，请重新选择！", Res.RM.GetString("任务助手"));
                return;
            }

            if (string.IsNullOrEmpty(taskTimesTextBox.Text))
            {
                MessageBox.Show("您没有设置重复次数，请重新设置！", Res.RM.GetString("任务助手"));
                return;
            }
            else
            {
                int times = Convert.ToInt32(taskTimesTextBox.Text);
                if (times <= 0 || times > 1000)
                {
                    MessageBox.Show("重复次数需为大于0且小于等于1000，请重新设置！", Res.RM.GetString("任务助手"));
                    return;
                }
                if (times > 100)
                {
                    if(MessageBox.Show("循环次数超过100所引起的程序假死请耐心等待！关闭游戏所造成的损失自己负责!", Res.RM.GetString("任务助手"), MessageBoxButtons.OKCancel)==DialogResult.Cancel) return;
                }
                UserInfo user = new DataProcess().ReadUserInfo();
                if (string.IsNullOrEmpty(user.TaskHelper) || !user.TaskHelper.Contains("buy"))
                {
                    int sum = times < 10 ? 200000 : 20000 * times;
                    if (Convert.ToInt64(user.金币) < sum)
                    {
                        MessageBox.Show("金币不足！", Res.RM.GetString("任务助手"));
                        return;
                    }
                    else
                    {
                        user.金币 = (Convert.ToInt64(user.金币) - sum).ToString();
                        new DataProcess().SaveUserDataFile(user);
                    }
                    
                }
                
                string taskId = TaskTable1[taskcomboBox1.Text];
                Stopwatch sw = new Stopwatch();
                sw.Start();
                new DataProcess().ReceiveTask(taskId);
                bool nostop = true;
                label14.Text = "状态：执行任务中。";
                autoTask = true;
                for (int i = 0; i < times; i++)
                {
                    if (new DataProcess().FulfilTask(taskId))
                    {
                        if (!new DataProcess().ReceiveTask(taskId))
                        {
                            MessageBox.Show("游戏内部错误，请重启游戏！");
                            break;
                        }
                    }
                    else
                    {
                        sw.Stop();
                        long ts = sw.ElapsedMilliseconds;
                        MessageBox.Show($"已帮您自动完成{i}次任务（由于任务必需品不足中止），共用时{ts}ms。",
                            Res.RM.GetString("任务助手"));
                        nostop = false;
                        break;
                    }
                }
                autoTask = false;
                if (nostop)
                {
                    sw.Stop();
                    long t = sw.ElapsedMilliseconds;
                    MessageBox.Show($"已帮您自动完成{times}次任务，共用时{t}ms。", Res.RM.GetString("任务助手"));
                }                  
                label14.Text = "状态：上一任务已完成，等待玩家命令中。";
            }
        }
    }
}
