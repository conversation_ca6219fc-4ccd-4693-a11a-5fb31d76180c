﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ShikongPlus.Pokemon2.PCG.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ShikongPlus.Pokemon2.PCG.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 自动地狱指令为：“/AutoHell 大层数（1至3000） 小层数（1至10）”&lt;/br&gt;自动通天指令为：“/AutoTT 层数（1至500）”。 的本地化字符串。
        /// </summary>
        internal static string AutoHelp {
            get {
                return ResourceManager.GetString("AutoHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 时空单机指令列表：&lt;/br&gt;输入&quot;/改名 要改的名字&quot;改名；&lt;/br&gt;输入“/备份”以打开备份存档目录；&lt;/br&gt;输入&quot;/更新日志&quot;查看更新日志；&lt;/br&gt;输入&quot;/NOBOSS&quot;可避免刷怪数达到要求后进入必遇BOSS模式（游戏重启失效）；&lt;/br&gt;输入&quot;/掉落查询&quot;可进入地图掉落查询模式；&lt;/br&gt;输入&quot;/换角色 数字1到6&quot;更换角色；&lt;/br&gt;输入&quot;/Auto?&quot;可查询自动地狱通天指令；&lt;/br&gt;输入&quot;/VIP&quot;可查询当前VIP等级所享受的福利；&lt;/br&gt;输入&quot;/宠物图鉴&quot;可观看宠物图鉴。&lt;/br&gt;输入&quot;/AutoFB?&quot;查看自动副本帮助。&lt;/br&gt;输入&quot;/AutoFB&quot;开始自动副本。&lt;/br&gt;输入&quot;/StopFB&quot;停止一切自动功能&lt;/br&gt;如有更多问题，可在交流群内寻求帮助。 的本地化字符串。
        /// </summary>
        internal static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Cheating?Naive! 的本地化字符串。
        /// </summary>
        internal static string Naive {
            get {
                return ResourceManager.GetString("Naive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请尝试减少一次购买的数量，单次购买使用的货币过多，系统无法运算。 的本地化字符串。
        /// </summary>
        internal static string Naive1 {
            get {
                return ResourceManager.GetString("Naive1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 严正警告 的本地化字符串。
        /// </summary>
        internal static string 严正警告 {
            get {
                return ResourceManager.GetString("严正警告", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 检测到有新补丁，请及时更新！ 的本地化字符串。
        /// </summary>
        internal static string 主程序有更新 {
            get {
                return ResourceManager.GetString("主程序有更新", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 任务助手 的本地化字符串。
        /// </summary>
        internal static string 任务助手 {
            get {
                return ResourceManager.GetString("任务助手", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 作弊代码： 的本地化字符串。
        /// </summary>
        internal static string 作弊代码 {
            get {
                return ResourceManager.GetString("作弊代码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 出现错误！错误代码：0x000001F 的本地化字符串。
        /// </summary>
        internal static string 修改器警告 {
            get {
                return ResourceManager.GetString("修改器警告", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 2秒钟后，将打开充值页面，请仔细检查所打开页面是否为时空单机的充值页面。账号名为论坛账号，如充错或长时间不到账请联系管理员。感谢您的支持与厚爱！Tips:扫描启动器界面的支付宝红包，充值时使用支付宝可抵扣相应金额！ 的本地化字符串。
        /// </summary>
        internal static string 充值公告 {
            get {
                return ResourceManager.GetString("充值公告", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请不要试图回到老版本哦！ 的本地化字符串。
        /// </summary>
        internal static string 勿回老版 {
            get {
                return ResourceManager.GetString("勿回老版", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 本游戏无法在虚拟机中运行！ 的本地化字符串。
        /// </summary>
        internal static string 反虚拟机 {
            get {
                return ResourceManager.GetString("反虚拟机", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请注意：变脸是将副宠cc按一定比例转移到主宠而非叠加，且只有添加百炼金丹后才能转移非天赋技能，您确定要这样做吗？ 的本地化字符串。
        /// </summary>
        internal static string 变脸提示 {
            get {
                return ResourceManager.GetString("变脸提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请勿使用变速齿轮等加速软件或篡改系统时间！ 的本地化字符串。
        /// </summary>
        internal static string 变速齿轮提示 {
            get {
                return ResourceManager.GetString("变速齿轮提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 名字长度不能超过20个半角字符、不能为空且不能有特殊字符！宠物名字不能为0！ 的本地化字符串。
        /// </summary>
        internal static string 命名规则提示 {
            get {
                return ResourceManager.GetString("命名规则提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 出现错误！错误代码：0x0000D1F 的本地化字符串。
        /// </summary>
        internal static string 多次作弊警告 {
            get {
                return ResourceManager.GetString("多次作弊警告", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统检测到您的存档已损坏，已为您打开备份目录，请尝试挨个恢复离现在时间最近的备份，直至不提示损坏为止！ 的本地化字符串。
        /// </summary>
        internal static string 存档损坏提示 {
            get {
                return ResourceManager.GetString("存档损坏提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 存档版本过低，请联系管理员进行处理！ 的本地化字符串。
        /// </summary>
        internal static string 存档版本过低 {
            get {
                return ResourceManager.GetString("存档版本过低", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请用您论坛论坛账号所绑定的邮箱发送“重置密码 新密码”至******************，我们会在一个工作日内回复您。 的本地化字符串。
        /// </summary>
        internal static string 忘记密码 {
            get {
                return ResourceManager.GetString("忘记密码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 感谢您对游戏开发组的大力支持，奖励已发放！ 的本地化字符串。
        /// </summary>
        internal static string 感谢支持 {
            get {
                return ResourceManager.GetString("感谢支持", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统监测到您使用了按键精灵，开发组对玩家使用按键精灵持开放态度，不作任何限制。但是，玩家需自行承担使用按键精灵可能带来的作弊误报风险和误操作损失。您确定还要继续使用按键精灵吗？ 的本地化字符串。
        /// </summary>
        internal static string 按键提示 {
            get {
                return ResourceManager.GetString("按键提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 更换角色成功，游戏将为您自动刷新页面！ 的本地化字符串。
        /// </summary>
        internal static string 换角色成功 {
            get {
                return ResourceManager.GetString("换角色成功", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 提示 的本地化字符串。
        /// </summary>
        internal static string 提示 {
            get {
                return ResourceManager.GetString("提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请勿篡改游戏基本配置！ 的本地化字符串。
        /// </summary>
        internal static string 改配置警告 {
            get {
                return ResourceManager.GetString("改配置警告", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 检测到游戏有新版本，您是否要立刻更新？ 的本地化字符串。
        /// </summary>
        internal static string 新版本提醒 {
            get {
                return ResourceManager.GetString("新版本提醒", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 无法获取活动地图！ 的本地化字符串。
        /// </summary>
        internal static string 无法获取活动地图 {
            get {
                return ResourceManager.GetString("无法获取活动地图", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 每日礼包 的本地化字符串。
        /// </summary>
        internal static string 每日礼包 {
            get {
                return ResourceManager.GetString("每日礼包", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 您今天已经领取过每日礼包了！ 的本地化字符串。
        /// </summary>
        internal static string 每日礼包已领取 {
            get {
                return ResourceManager.GetString("每日礼包已领取", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 获取网络时间失败,无法领取每日礼包（若今日已领过，请无视）！ 的本地化字符串。
        /// </summary>
        internal static string 每日礼包领取失败 {
            get {
                return ResourceManager.GetString("每日礼包领取失败", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 领取每日礼包成功，祝您游戏愉快！ 的本地化字符串。
        /// </summary>
        internal static string 每日礼包领取成功 {
            get {
                return ResourceManager.GetString("每日礼包领取成功", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 您的电脑上未安装QQ或QQ版本过低，无法与客服交流！ 的本地化字符串。
        /// </summary>
        internal static string 没装QQ {
            get {
                return ResourceManager.GetString("没装QQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 注册成功，游戏将在5秒后自动关闭以配置信息。您重启游戏后即可开始游戏！ 的本地化字符串。
        /// </summary>
        internal static string 注册成功 {
            get {
                return ResourceManager.GetString("注册成功", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 该存档为测试存档,无法继续游戏！ 的本地化字符串。
        /// </summary>
        internal static string 测试存档 {
            get {
                return ResourceManager.GetString("测试存档", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 游戏已运行！ 的本地化字符串。
        /// </summary>
        internal static string 游戏已运行 {
            get {
                return ResourceManager.GetString("游戏已运行", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 检测到新版本，请及时更新！ 的本地化字符串。
        /// </summary>
        internal static string 游戏版本过低 {
            get {
                return ResourceManager.GetString("游戏版本过低", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 登录成功！ 的本地化字符串。
        /// </summary>
        internal static string 登录成功 {
            get {
                return ResourceManager.GetString("登录成功", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 称号类道具无法丢弃！ 的本地化字符串。
        /// </summary>
        internal static string 禁止丢弃 {
            get {
                return ResourceManager.GetString("禁止丢弃", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 你已被禁止进入该版本 的本地化字符串。
        /// </summary>
        internal static string 禁止进入该版本 {
            get {
                return ResourceManager.GetString("禁止进入该版本", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请勿私下交易或交换存档！如果您确实在多个电脑上游戏，请运行tools目录下的信息收集工具，将结果和存档交给管理员处理！ 的本地化字符串。
        /// </summary>
        internal static string 私自换存档 {
            get {
                return ResourceManager.GetString("私自换存档", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 网络有问题，正在重试！ 的本地化字符串。
        /// </summary>
        internal static string 网络有问题正在重试 {
            get {
                return ResourceManager.GetString("网络有问题正在重试", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 聊天连接出现异常。 的本地化字符串。
        /// </summary>
        internal static string 聊天异常 {
            get {
                return ResourceManager.GetString("聊天异常", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 获取云服务器ip失败，请重试！ 的本地化字符串。
        /// </summary>
        internal static string 获取ip失败 {
            get {
                return ResourceManager.GetString("获取ip失败", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请勿把新版本覆盖解压到老版本上！ 的本地化字符串。
        /// </summary>
        internal static string 覆盖解压 {
            get {
                return ResourceManager.GetString("覆盖解压", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 警告 的本地化字符串。
        /// </summary>
        internal static string 警告 {
            get {
                return ResourceManager.GetString("警告", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 如有误报，请勿再开游戏，立即联系开发组进行处理！ 的本地化字符串。
        /// </summary>
        internal static string 误报提示 {
            get {
                return ResourceManager.GetString("误报提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请问您是否需要游戏帮您自动转移老版本存档至该版本？注意：新版本不能覆盖解压在老版本上，否则转移会失败。 的本地化字符串。
        /// </summary>
        internal static string 转移存档 {
            get {
                return ResourceManager.GetString("转移存档", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 转移存档成功，系统将在5秒后自动关闭以配置信息。您重启游戏即可开始游戏！ 的本地化字符串。
        /// </summary>
        internal static string 转移成功 {
            get {
                return ResourceManager.GetString("转移成功", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 检测到游戏进程被破坏，请尝试将启动器和主程序加入到杀毒软件信任区！ 的本地化字符串。
        /// </summary>
        internal static string 进程被破坏 {
            get {
                return ResourceManager.GetString("进程被破坏", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似  的本地化字符串。
        /// </summary>
        internal static string 连接错误 {
            get {
                return ResourceManager.GetString("连接错误", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 退出游戏 的本地化字符串。
        /// </summary>
        internal static string 退出 {
            get {
                return ResourceManager.GetString("退出", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 您确定要退出游戏吗？ 的本地化字符串。
        /// </summary>
        internal static string 退出确认 {
            get {
                return ResourceManager.GetString("退出确认", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统检测到您的存档绑定了云服务器，请问您现在在云服务器进行游戏吗？ 的本地化字符串。
        /// </summary>
        internal static string 选择模式 {
            get {
                return ResourceManager.GetString("选择模式", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 检测到您已创立了存档，请勿重复注册！ 的本地化字符串。
        /// </summary>
        internal static string 重复注册提示 {
            get {
                return ResourceManager.GetString("重复注册提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 该副本还未打完，确定要重置该副本吗？ 的本地化字符串。
        /// </summary>
        internal static string 重置确认 {
            get {
                return ResourceManager.GetString("重置确认", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 点击阅读《时空游戏用户条款》 的本地化字符串。
        /// </summary>
        internal static string 阅读条款 {
            get {
                return ResourceManager.GetString("阅读条款", resourceCulture);
            }
        }
    }
}
