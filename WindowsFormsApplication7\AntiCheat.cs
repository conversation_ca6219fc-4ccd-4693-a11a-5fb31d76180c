﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using Microsoft.Win32;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    internal class AntiCheat
    {
        internal static string FinalPath =
            Path.Combine(DataProcess.ApplicationDataPath, @"Tencent\Config\p2p_200014357082.ini");

        internal static string FP1 =
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), @"txsso.db");

        internal static string FP2 = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            @"u7x5t4e.dll");

        internal static bool AnJian = false;
        private static bool _warning = false;

        internal static void Anti_AnJian()
        {
            bool aj = false;

            Process[] jclb = Process.GetProcesses();
            foreach (Process process in jclb)
            {
                if (process.MainWindowTitle.Contains("按键精灵") || process.ProcessName.Contains("按键精灵"))
                {
                    aj = true;
                }
            }

            string path = string.Empty;


            try
            {
                RegistryKey ajml =
                    Registry.ClassesRoot.CreateSubKey(
                        "WOW6432Node\\CLSID\\{EBEB87A4-E151-4054-AB45-A6E094C5334B}\\LocalServer32");

                if (ajml?.GetValue("") != null)
                {
                    path = ajml.GetValue("").ToString();
                }
                //Console.WriteLine(path);
            }
            catch
            {
                //Console.WriteLine("1");
            }


            if (!string.IsNullOrWhiteSpace(path) && File.Exists(path))
            {
                aj = new NativeMethods.IsInUse().IsFileInUse(path);
            }
        
            if (aj)
            {
                AnJian = true;
                PetProcess.Effect = 0.6;
                LogSystem.JoinLog(LogSystem.EventKind.按键精灵, "Find By TimerTask");
                if (!_warning)
                {
                    DialogResult dr = MessageBox.Show(Res.RM.GetString("按键提示"), Res.RM.GetString("警告"),
                        MessageBoxButtons.OKCancel);

                    if (dr == DialogResult.Cancel)
                    {
                        Tools.ForcedExit("不同意按键条款");
                    }
                    else if (dr == DialogResult.OK)
                    {
                        _warning = true;
                    }
                }
            }

                
        }

        internal void CheckProcess()
        {
            Process[] jclb = Process.GetProcesses();
            int c = 0;
            foreach (Process process in jclb)
            {
                if (process.MainWindowTitle.ToUpper().Contains("CHEATENGINE") || process.MainWindowTitle.ToUpper() == "OLLYDBG" 
                    || process.MainWindowTitle.ToUpper() == "OLLYICE" || process.MainWindowTitle.ToUpper() == "MHSCN" 
                    || process.MainWindowTitle.ToUpper() == "KNIGHTV" || process.MainWindowTitle == "吾爱破解[LCG]" 
                    || process.MainWindowTitle.ToUpper() == "VZLA ENGINE" || process.MainWindowTitle.Contains("金山游侠"))
                {
                    process.Kill();
                    CheatCodeMsg("000（请勿使用内存修改器）");
                    PunishmentProcess(0);
                }

                /*if (process.MainWindowTitle.Contains("加速") && process.MainWindowTitle.Contains("单机"))
                {
                    process.Kill();
                    CheatCodeMsg("000（请勿使用变速工具）");
                    PunishmentProcess(0);
                }*/
                if (/*process.MainWindowTitle.Contains("变速") || */process.MainWindowTitle.ToUpper().Contains("齿轮") || process.MainWindowTitle.ToUpper().Contains("GEARNT"))
                {
                    process.Kill();
                    CheatCodeMsg("000（请勿使用变速工具）");
                    PunishmentProcess(0);
                }
                
                /*if ((process.ProcessName.Contains("修改") || process.ProcessName.Contains("作弊") || process.ProcessName.Contains("GM工具")) && (process.ProcessName.Contains("时空口袋") || process.ProcessName.Contains("口袋时空") || process.ProcessName.Contains("时空单机")))
                {
                    process.Kill();
                    CheatCodeMsg("001");
                    PunishmentProcess(1);
                }*/
                if (process.ProcessName.Equals("时空单机启动器"))
                {
                    c = 1;
                }
            }

            if (c != 1 && DataProcess.AdminMode == 0 && !Program.getDebug())
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("进程被破坏"), Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("监视器进程被破坏");
            }

            if (PetProcess.Effect > 1.0)
            {
                CheatCodeMsg("000（请勿使用内存修改器）");
                PunishmentProcess(0);
            }

        }

        private static void NotYourData()
        {
            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("私自换存档"), Res.RM.GetString("警告"), 5000);
            Tools.ForcedExit("私自换存档");
        }

        internal static void AntiCheat_A()
        {
            UserInfo user = new DataProcess().ReadUserInfo();

            if (Convert.ToInt32(user.版本号) > Convert.ToInt32(DataProcess.Version))
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("勿回老版"), Res.RM.GetString("警告"), 5000);
                Tools.ForcedExit("尝试回老版本");
            }

            if (string.IsNullOrEmpty(user.NB1) && string.IsNullOrEmpty(user.NB2) && string.IsNullOrEmpty(user.NB3))
            {
                Tools.ForcedExit("存档无硬件信息");
            }

            if (string.IsNullOrEmpty(DataProcess.DiskInfo) && string.IsNullOrEmpty(DataProcess.IP))
            {
                DataProcess.GetMachineInfo();
            }

            if (DataProcess.EnvironmentMode == 1)
            {
                try//这里用tyr包起来,防止有人不看信息直接点是
                {
                    if (!DataProcess.IP.Equals(user.NB1))
                    {
                        NotYourData();
                    }
                }catch(Exception)
                {
                    MessageBox.Show("不是绑定的云端,或则在自己电脑上选择了是!","提示");
                    Environment.Exit(0);
                }
                
            }
            else if (DataProcess.EnvironmentMode == 0)
            {

                if (!string.IsNullOrEmpty(user.NB1) && DataProcess.DiskInfo == user.NB1
                    || !string.IsNullOrEmpty(user.NB2) && DataProcess.DiskInfo == user.NB2
                    || !string.IsNullOrEmpty(user.NB3) && DataProcess.DiskInfo == user.NB3)
                {

                }
                else
                {
                    NotYourData();
                }

            }
            else
            {
                Tools.ForcedExit("运行环境模式异常");
            }          
        }
        //道具数量检测
        internal static Dictionary<string, int> CheckList = new Dictionary<string, int>
        {
            {"2017062404", 50000},//凤凰珠
            {"20161126", 50000},//伊苏王礼包
            {"943016", 50000},//法老王的礼包
            //{"2017021601", 100000},//BUFF升级书
            //{"2017021602", 100000},//技能升级书
            {"2017061401", 500000},//修炼仙册[嗔]
            {"2022010607", 10000},//天使药水-修炼仙册[嗔]
            {"2016102001", 10000},//每日礼包
            {"2017092001",10000},//五行点化石
            {"2017080705",10000},//1E金币券
            {"2017060302",2100000000},//强化石
            {"2016101802",1000 },//滑稽女神之卵
            { "2020061403",4},//各种超值礼包
            { "2020081101",4},//各种超值礼包
            { "2020111001",4},//各种超值礼包
            { "2020121101",4},//各种超值礼包
            { "2021030803",4},//各种超值礼包
            { "2021030804",4},//各种超值礼包
            { "2021032608",4},//各种超值礼包
            { "10002",1},//我的专属称号
            { "2019040805",200},//≮残酷≯之卵大礼包
        };


        internal static void AntiCheat_B()
        {
            try
            {
                new DataProcess().GetPAP();
                new DataProcess().AntiBug1();
                foreach (PropInfo 待检测道具 in DataProcess.PP_List)
                {
                    if (CheckList.ContainsKey(待检测道具.道具类型ID))
                    {
                        if (Convert.ToInt32(待检测道具.道具数量) > CheckList[待检测道具.道具类型ID] && !new DataProcess().getPower())//提供ID[Key]返回value
                        {
                            int s = CheckList[待检测道具.道具类型ID];
                            CheatCodeMsg("01A");
                            PunishmentProcess(2);
                        }
                    }
                    else
                    {
                         if (Convert.ToInt32(待检测道具.道具数量) > 10000000 && !new DataProcess().getPower())
                        {
                            CheatCodeMsg("01B");
                            PunishmentProcess(2);
                        }
                    }

                    /*if (待检测道具.道具类型ID.Equals("2017062404") || 待检测道具.道具类型ID.Equals("20161126") || 待检测道具.道具类型ID.Equals("943016") || 待检测道具.道具类型ID.Equals("2017021601") || 待检测道具.道具类型ID.Equals("2017021602") || 待检测道具.道具类型ID.Equals("2017061401"))
                    {
                        if (Convert.ToInt32(待检测道具.道具数量) > 10000)
                        {
                            CheatCodeMsg("010");
                            PunishmentProcess(1);
                        }
                    }

                    if (待检测道具.道具类型ID.Equals("2016102001") && Convert.ToInt32(待检测道具.道具数量) > 1000)
                    {
                        CheatCodeMsg("012");
                        PunishmentProcess(2);
                    }
                    if (Convert.ToInt32(待检测道具.道具数量) > 10000000 && !待检测道具.道具类型ID.Equals("2017060302"))
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(1);
                    }
                    if (待检测道具.道具类型ID.Equals("2017060302") && Convert.ToInt32(待检测道具.道具数量) > 50000000)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(2);
                    }
                    if (待检测道具.道具类型ID.Equals("2017021503") && Convert.ToInt32(待检测道具.道具数量) > 50)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(2);
                    }
                    if (待检测道具.道具类型ID.Equals("2017092001") && Convert.ToInt32(待检测道具.道具数量) > 50)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(1);
                    }
                    if (待检测道具.道具类型ID.Equals("2017080705") && Convert.ToInt32(待检测道具.道具数量) > 100)
                    {
                        CheatCodeMsg("013");
                        PunishmentProcess(2);
                    }*/
                    /*if (Convert.ToInt32(待检测道具.道具类型ID) >= 943029 && Convert.ToInt32(待检测道具.道具类型ID) <= 943038)
                    {
                        if (Convert.ToInt32(待检测道具.道具数量) > 1)
                        {
                            作弊代码通知("011");
                            作弊处理过程(1);
                        }
                    }*/
                }
            }
            catch (Exception ex)
            {
                 throw ex;
            }
        }
        internal static void AntiCheat_C()
        {
            UserInfo 用户 = new DataProcess().ReadUserInfo();
            short grade = Convert.ToInt16(用户.vip);
            int credit = Convert.ToInt32(用户.VIP积分);
            if (grade < 10)
            {
                //如果VIP等级小于10，且有VIP积分
                if (credit > 0)
                {
                    CheatCodeMsg("020");
                    PunishmentProcess(2);
                }
                if (用户.至尊VIP)
                {
                    CheatCodeMsg("023");
                    PunishmentProcess(2);
                }
                if (用户.星辰VIP)
                {
                    CheatCodeMsg("024");
                    PunishmentProcess(2);
                }
            }
            else if (grade == 10)
            {
                if (credit > 10000000)//VIP积分超过10000000判断为作弊
                {
                    CheatCodeMsg("021");
                    PunishmentProcess(2);
                }
            }
            else if (grade > 10)
            {
                CheatCodeMsg("022");
                PunishmentProcess(2);
            }

            if (Convert.ToInt16(用户.道具容量) > 500 && ! new DataProcess().getPower())//检测道具格子
            {
                CheatCodeMsg("B50");
                PunishmentProcess(2);
            }

            if (Convert.ToInt16(用户.牧场容量) > 120)//检测牧场格子
            {
                CheatCodeMsg("B20");
                PunishmentProcess(2);
            }
        }
        public static void Cheating_Punishment()
        {
            new DataProcess().ReadUserInfo1();

            //Directory.Delete(DataProcess.TenMinBackup, true);

            File.WriteAllText(FP1, "");
            File.WriteAllText(FP2, "");


            PunishmentProcess(0, 1);
        }
        internal static void Checking_CheatingHistory()
        {
            if (File.Exists(FinalPath) || File.Exists(FP1)|| File.Exists(FP2))
            {
                Cheating_Punishment();
                PunishmentProcess(0, 1);
            }
        }
        
        internal static void PunishmentProcess(int bnum, int 多次 = 0)
        {
            if (bnum != 0)
            {
                UserInfo 用户 = new DataProcess().ReadUserInfo();
                用户.b = (Convert.ToInt16(用户.b) + bnum).ToString();
                new DataProcess().SaveUserDataFile(用户);

                if (Convert.ToInt16(用户.b) >= 2)
                {
                    Cheating_Punishment();
                }
            }
            if (多次 == 0)
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("修改器警告"), Res.RM.GetString("严正警告"), 2000);
            }
            else if (多次 == 1)
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("多次作弊警告"), Res.RM.GetString("严正警告"), 2000);
            }
            Tools.ForcedExit("作弊或尝试作弊处理");
        }
        internal static void TamperingProcess()
        {
            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("改配置警告"), Res.RM.GetString("严正警告"), 2000);
            Tools.ForcedExit("篡改配置");
        }
        internal static void CheatCodeMsg(string kind)
        {
            SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("作弊代码") + kind + Res.RM.GetString("误报提示"), Res.RM.GetString("严正警告"), 5000);
        }
    }
}
