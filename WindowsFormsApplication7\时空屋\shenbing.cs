﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shikong.Pokemon2.PCG.时空屋
{
    public class shenbing
    {
        public int 套装ID { get; set; }
        public shenbing_sheding 兵魂 { get; set; }
        public shenbing_sheding 神兵 { get; set; }
        public shenbing_sheding 巫灵 { get; set; }
        /// <summary>
        /// 升级神兵
        /// </summary>
        /// <param name="id">神兵ID</param>
        /// <returns></returns>
        public static string up(String id) {
            var user = new DataProcess().ReadUserInfo();
            if (user.神兵列表 == null) user.神兵列表 = new Dictionary<string, int>();
            if (user.神兵列表.ContainsKey(id))
            {
                var shenbings = GetShenbings();//读取所有神兵类型
                var s = shenbings.FirstOrDefault(C => C.兵魂.名字 == id || C.巫灵.名字 == id || C.神兵.名字 == id);
                if (s == null) {
                    return "神兵不在规则中存在，请联系管理员检查！";
                }
                shenbing_qhsd sheding = null;//强化设定
                shenbing_sheding that = null;
                if (s.兵魂.名字 == id) {
                    sheding = s.兵魂.强化设定;
                    that = s.兵魂;
                }
                if (s.神兵.名字 == id)
                {
                    sheding = s.神兵.强化设定;
                    that = s.神兵;
                }
                if (s.巫灵.名字 == id)
                {
                    sheding = s.巫灵.强化设定;
                    that = s.巫灵;
                }
                if (that == null) {
                    return "神兵不在规则中存在，请联系管理员检查！";
                }
                if (Convert.ToInt32(user.神兵列表[id]) >= sheding.最高等级) {
                    return "当前已经满级，无法继续强化！";
                }
                if (!sheding.所需数量.ContainsKey(Convert.ToInt32(user.神兵列表[id]) + 1)) {
                    return "规则中没有设定下一级所需的道具数量，请联系管理员检查！";
                }
                if (!that.属性.ContainsKey(Convert.ToInt32(user.神兵列表[id]) + 1))
                {
                    return "规则中没有设定下一级神兵的属性，因此无法强化，请联系管理员检查！";
                }
                var propType = new DataProcess().GetAPType(sheding.所需道具);
                if (propType == null) {
                    return "所需道具不存在于当前版本，请联系管理员检查！";
                }
                var prop = new DataProcess().GetAP_ID(sheding.所需道具);
                var propNum = sheding.所需数量[Convert.ToInt32(user.神兵列表[id]) + 1];
                if (prop == null || Convert.ToInt32(prop.道具数量) < propNum)
                {
                    return "强化所需道具不足，无法强化！需要" + propType.道具名字 + "*" + propNum;
                }
                new DataProcess().ReviseOrDeletePP(prop, propNum);
                user.神兵列表[id]++;
                new DataProcess().SaveUserDataFile(user);
                return "成功将【" + id + "】的等级强化到：" + user.神兵列表[id];
            }
            else {
                return "神兵不存在！";
            }
            return "成功强化！";
        
        }
        /// <summary>
        /// 获取所有已激活的神兵设定，一般用来计算属性
        /// </summary>
        public static List<shenbing_sheding> getALL() {
            var user = new DataProcess().ReadUserInfo();
            var shenbings = GetShenbings();//读取所有神兵类型
            var list = new List<shenbing_sheding>();
            if (user.神兵列表==null) user.神兵列表 = new Dictionary<string, int>();
            //shenbings.Where(C => C.兵魂.名字 == id || C.巫灵.名字 == id || C.神兵.名字 == id).;
            foreach (var s in user.神兵列表) {

                var l= shenbings.FirstOrDefault(C => C.兵魂.名字 == s.Key || C.巫灵.名字 == s.Key || C.神兵.名字 == s.Key);
                var sheding = new shenbing_sheding();

                if (l.兵魂.名字 == s.Key)
                {
                    sheding = l.兵魂;
                   
                }
                if (l.神兵.名字 == s.Key)
                {
                    sheding = l.神兵;
                }
                if (l.巫灵.名字 == s.Key)
                {
                    sheding = l.巫灵;
                }
                //请注意，在时空服中使用该代码的时候要小心代码污染，建议重复序列化后使用
                //单机请忽视以上注释
                sheding.当前等级 = s.Value;
                if (l != null) list.Add(sheding);
            }
            return list;
        }
        /// <summary>
        /// 获取所有神兵的ID（每三个为一组，这里的ID为这个组的ID）
        /// </summary>
        /// <returns></returns>
        public static List<int> getSBClassList() {
            var sbs = GetShenbings();
            var user = new DataProcess().ReadUserInfo();
            List<int> all = new List<int>();
            foreach (var u in user.神兵列表) {
                var s = sbs.FirstOrDefault(C => C.兵魂.名字.Equals(u.Key) ||
                C.兵魂.名字.Equals(u.Key) || C.巫灵.名字.Equals(u.Key));
                if (s != null) {
                    if(all.Exists(C=>C==s.套装ID))all.Add(s.套装ID);
                }
            }
            return all;
        }
        /// <summary>
        /// 计算所有属性
        /// </summary>
        /// <returns>返回一个词典，代表每个属性的值</returns>
        public static Dictionary<String, double> calcALL() {
            var shenbingList = shenbing.getALL();
            var calcResult = new Dictionary<String, double>();
            calcResult.Add("攻击", 0);
            calcResult.Add("命中", 0);
            calcResult.Add("防御", 0);
            calcResult.Add("速度", 0);
            calcResult.Add("闪避", 0);
            calcResult.Add("生命", 0);
            calcResult.Add("魔法", 0);
            calcResult.Add("加深", 0);
            calcResult.Add("抵消", 0);
            calcResult.Add("吸血", 0);
            calcResult.Add("吸魔", 0);
            foreach (var sb in shenbingList) {
                foreach (var k in sb.属性[sb.当前等级]) {
                    calcResult[k.Key] += k.Value;
                }
            }
            return calcResult;
        }
        public const string SB_Path = @"PageMain\equip_1.qingshan"; //神兵 
        /// <summary>
        /// 获取定义列表
        /// </summary>
        /// <param name="getname">是否将所需道具的道具ID转换为道具名，一般拿来展示时使用</param>
        /// <returns></returns>
        public static List<shenbing> GetShenbings(bool getname = false)
        {
            string cfg = new DataProcess().ReadFile( DataProcess.pf +SB_Path);

            if (cfg == null || cfg.Length <= 0)
            {
                return new List<shenbing>();
            }

            cfg = SkRC4.DES.DecryptRC4(cfg, new DataProcess().GetKey(1));
            var way = JsonConvert.DeserializeObject<List<shenbing>>(cfg);
            if (getname)
            {
                for (int i = 0; i < way.Count; i++)
                {
                    var w = way[i];
                    w.兵魂.强化设定.所需道具 = new DataProcess().GetAPType(w.兵魂.强化设定.所需道具).道具名字;
                    w.巫灵.强化设定.所需道具 = new DataProcess().GetAPType(w.巫灵.强化设定.所需道具).道具名字;
                    w.神兵.强化设定.所需道具 = new DataProcess().GetAPType(w.神兵.强化设定.所需道具).道具名字;
                }
            }
            DataProcess.stringLoadSBJSON = cfg;
            return way;
        }

    }
    public class shenbing_sheding
    {
        public string 名字 { get; set; }
        public Dictionary<int, Dictionary<String, double>> 属性 { get; set; }
        public shenbing_qhsd 强化设定 { get; set; }
        //这个是在代码中运算的，配置中写为0或者留空即可
        public int 当前等级;
        //图片素材需要带后缀，举例：test.png，放在images/shenbing/bh1.png之中
        public string 图片素材 { get; set; }
    }
    public class shenbing_qhsd
    {
        public int 最高等级 { get; set; }
        public string 所需道具 { get; set; }
        public Dictionary<int, int> 所需数量 { get; set; }
    }

}
