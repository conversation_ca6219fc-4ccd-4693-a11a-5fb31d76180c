---
description: 
globs: 
alwaysApply: true
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

- 你是这个游戏的专家

- 使用的CSS必须兼容IE6，当需要布局的时候需要浮动float,如果有一些窗口是提示窗你需要绝对定位，不使用新的布局技术，因为需要兼容IE6,IE版本比较早

- 不需要写任何视觉效果，只需要保证功能正常即可

- 你可以使用jquery-1.8.3以及一些早期的原生语法（也需要考虑兼容IE6）

- 你只能使用css1和css2不能使用css3以及以上的技术，同样的js的写法也要适配这个版本

- css不能用百分比如 left: 50%;要用px

- 项目中使用的WebBrowser默认内核为IE6，编写html和css的时候要考虑到

- 项目采用的是CS/BS的结构,目标框架是.NET Framework 4.8，启动窗口是Form1

- HTML页面在bin\Debug\PageMain文件夹下，启动页面是index.html

- 突破玩法素材在bin\Debug\PageMain\images\chengzhangtup\ 路径下

- 成长突破功能加在时空殿堂中，时空殿堂页面是bin\Debug\PageMain\sksd.html


- 一、项目概览
- 项目名称/核心程序集: `PetShikongPlus` (基于 `PetShikong.csproj`)
- 项目类型: Windows Forms 桌面应用程序 (.NET Framework 4.8)
- 架构: CS/BS 结构 (客户端/服务端 + 浏览器/服务端)
- 目标框架: .NET Framework 4.8
- 根命名空间: `ShikongPlus.Pokemon2.PCG`

-  二、关键文件与目录结构
-  C# 服务端/桌面应用部分 (`WindowsFormsApplication7/` 目录)
- 启动窗口/主窗体: `Form1.cs` (位于 `WindowsFormsApplication7/Form1.cs`)
    - `Form1.cs` 文件较大 (约798KB, 4402行)，是核心交互界面。
- 主要数据处理: `DataProcess.cs` (位于 `WindowsFormsApplication7/DataProcess.cs`)
    - `DataProcess.cs` 文件非常大 (约404KB, 9603行)，包含大量业务逻辑。
- 项目配置文件:
    - `PetShikong.csproj`: MSBuild 项目文件，定义了项目结构、编译项和依赖。
    - `packages.config`: 列出 NuGet 包依赖，如 `Newtonsoft.Json` (v11.0.1)。
    - `App.config`: 应用程序配置文件。
- 主要源代码目录: `WindowsFormsApplication7/`
    - 包含众多 `.cs` 文件，如 `PlayerHelper.cs`, `PetCalc.cs`, `Fight.cs`, `UserInfo.cs` 等。
- 功能模块子目录 (在 `WindowsFormsApplication7/` 下):
    - `龙珠/`
    - `魂宠/`
    - `装备宝石/`
    - `时空屋/`
    - `占卜屋/`
    - `Tools/` (包含工具类如 `ShikongTools.cs`, `SkCryptography.cs`)
- 编译输出目录: `WindowsFormsApplication7/bin/Debug/` (针对 Debug 配置)

- HTML 客户端部分
- HTML 页面根目录: `WindowsFormsApplication7/bin/Debug/PageMain/`
- 启动 HTML 页面: `Index.html` (位于 `WindowsFormsApplication7/bin/Debug/PageMain/Index.html`)
- 其他 HTML 文件: 该目录下还包含 `Malls.html`, `petMain.html`, `Battle.html` 等多个功能页面。
- Web 资源: `js/`, `css/`, `img/`, `images/` 等子目录位于 `PageMain/` 下。

-  三、主要技术点与依赖
- UI 技术: Windows Forms (WinForms)
- 主要第三方库: `Newtonsoft.Json` (用于 JSON 处理)
- 语言: C#
- 开发环境相关:
    - `.vscode/` 或 `.vs/` 目录可能包含 IDE特定的配置。
    - `PetShikong.csproj.user` 包含用户特定的项目设置。

-  四、其他注意事项
- 项目中存在一些较大的 `.cs` 文件，分析时可能需要分块读取或针对性分析。
- `document_rules` 中提及的“你是这个游戏的专家”表明项目与游戏相关。
- 项目结构清晰，CS (C# WinForms) 和 BS (HTML/JS) 部分职责分明。
```


