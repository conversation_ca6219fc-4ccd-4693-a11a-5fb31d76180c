using System;
using System.Collections.Generic;

namespace ShikongPlus.Pokemon2.PCG.成长突破
{
    /// <summary>
    /// 成长突破等级配置
    /// </summary>
    public class BreakthroughConfig
    {
        public int level { get; set; }              // 突破等级
        public int stoneRequired { get; set; }      // 需要的突破圣石数量
        public int ccRequired { get; set; }         // 需要的成长值（万）
        public int ccMax { get; set; }              // 突破后成长上限（万）
        public double ccLossRate { get; set; }      // 成功后成长折损率
        public int baseSuccessRate { get; set; }    // 基础成功率
        public string raceRequired { get; set; }    // 需要的种族
    }

    /// <summary>
    /// 成长突破配置管理器
    /// </summary>
    public static class BreakthroughConfigManager
    {
        private static readonly List<BreakthroughConfig> configs = new List<BreakthroughConfig>
        {
            new BreakthroughConfig { level = 1, stoneRequired = 10, ccRequired = 2000, ccMax = 2100, ccLossRate = 0.10, baseSuccessRate = 80, raceRequired = "萌" },
            new BreakthroughConfig { level = 2, stoneRequired = 20, ccRequired = 2100, ccMax = 2300, ccLossRate = 0.15, baseSuccessRate = 70, raceRequired = "萌" },
            new BreakthroughConfig { level = 3, stoneRequired = 30, ccRequired = 2300, ccMax = 2500, ccLossRate = 0.15, baseSuccessRate = 60, raceRequired = "灵" },
            new BreakthroughConfig { level = 4, stoneRequired = 40, ccRequired = 2500, ccMax = 2700, ccLossRate = 0.20, baseSuccessRate = 50, raceRequired = "灵" },
            new BreakthroughConfig { level = 5, stoneRequired = 50, ccRequired = 2700, ccMax = 2900, ccLossRate = 0.20, baseSuccessRate = 45, raceRequired = "灵" },
            new BreakthroughConfig { level = 6, stoneRequired = 60, ccRequired = 2900, ccMax = 3100, ccLossRate = 0.25, baseSuccessRate = 40, raceRequired = "梦" },
            new BreakthroughConfig { level = 7, stoneRequired = 70, ccRequired = 3100, ccMax = 3300, ccLossRate = 0.25, baseSuccessRate = 35, raceRequired = "梦" },
            new BreakthroughConfig { level = 8, stoneRequired = 80, ccRequired = 3300, ccMax = 3500, ccLossRate = 0.30, baseSuccessRate = 30, raceRequired = "梦" },
            new BreakthroughConfig { level = 9, stoneRequired = 90, ccRequired = 3500, ccMax = 3700, ccLossRate = 0.30, baseSuccessRate = 25, raceRequired = "圣灵" },
            new BreakthroughConfig { level = 10, stoneRequired = 100, ccRequired = 3700, ccMax = 4000, ccLossRate = 0.35, baseSuccessRate = 20, raceRequired = "圣灵" }
        };

        /// <summary>
        /// 获取指定等级的突破配置
        /// </summary>
        public static BreakthroughConfig GetConfig(int level)
        {
            return configs.Find(c => c.level == level);
        }

        /// <summary>
        /// 获取所有突破配置
        /// </summary>
        public static List<BreakthroughConfig> GetAllConfigs()
        {
            return new List<BreakthroughConfig>(configs);
        }

        /// <summary>
        /// 获取最大突破等级
        /// </summary>
        public static int GetMaxLevel()
        {
            return 10;
        }
    }
} 