﻿namespace Shikong.Pokemon2.PCG
{
    partial class PlayerHelper
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PlayerHelper));
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.label6 = new System.Windows.Forms.Label();
            this.jhPropComBox = new System.Windows.Forms.ComboBox();
            this.timesTextBox = new System.Windows.Forms.TextBox();
            this.viceLvTextBox = new System.Windows.Forms.TextBox();
            this.mainLvTextBox = new System.Windows.Forms.TextBox();
            this.propComBox2 = new System.Windows.Forms.ComboBox();
            this.propComBox1 = new System.Windows.Forms.ComboBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.mainComboBox = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.viceComboBox = new System.Windows.Forms.ComboBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.label8 = new System.Windows.Forms.Label();
            this.checkedListBox1 = new System.Windows.Forms.CheckedListBox();
            this.label7 = new System.Windows.Forms.Label();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.label14 = new System.Windows.Forms.Label();
            this.button4 = new System.Windows.Forms.Button();
            this.label13 = new System.Windows.Forms.Label();
            this.taskTimesTextBox = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.taskcomboBox1 = new System.Windows.Forms.ComboBox();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.check_DXC = new System.Windows.Forms.CheckBox();
            this.label19 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.tabControl2 = new System.Windows.Forms.TabControl();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.AM_info = new System.Windows.Forms.TextBox();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.AM_EXP = new System.Windows.Forms.Label();
            this.AM_yb = new System.Windows.Forms.Label();
            this.AM_money = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.AM_proplist = new System.Windows.Forms.DataGridView();
            this.道具名字 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.数量 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.AM_BOSS = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.AM_loopnum = new System.Windows.Forms.Label();
            this.AM_battlenum = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.AM_fornum = new System.Windows.Forms.TextBox();
            this.AM_start = new System.Windows.Forms.Button();
            this.AM_looplabel = new System.Windows.Forms.Label();
            this.AM_petname = new System.Windows.Forms.Label();
            this.AM_skill = new System.Windows.Forms.ComboBox();
            this.label16 = new System.Windows.Forms.Label();
            this.AM_map = new System.Windows.Forms.ComboBox();
            this.label15 = new System.Windows.Forms.Label();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.checkBox_FIGHTFAST = new System.Windows.Forms.CheckBox();
            this.checkBox_FIGHT_DEBUG = new System.Windows.Forms.CheckBox();
            this.panel_FIGHT_DEBUG = new System.Windows.Forms.Panel();
            this.textBox_BattleNum = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.FIGHT_ATK = new System.Windows.Forms.CheckBox();
            this.AutoMapTimer = new System.Windows.Forms.Timer(this.components);
            this.tabPage1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tabControl2.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.tabPage6.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.AM_proplist)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage7.SuspendLayout();
            this.panel_FIGHT_DEBUG.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabPage1
            // 
            this.tabPage1.BackColor = System.Drawing.Color.Gainsboro;
            this.tabPage1.Controls.Add(this.label6);
            this.tabPage1.Controls.Add(this.jhPropComBox);
            this.tabPage1.Controls.Add(this.timesTextBox);
            this.tabPage1.Controls.Add(this.viceLvTextBox);
            this.tabPage1.Controls.Add(this.mainLvTextBox);
            this.tabPage1.Controls.Add(this.propComBox2);
            this.tabPage1.Controls.Add(this.propComBox1);
            this.tabPage1.Controls.Add(this.label11);
            this.tabPage1.Controls.Add(this.label10);
            this.tabPage1.Controls.Add(this.label9);
            this.tabPage1.Controls.Add(this.button1);
            this.tabPage1.Controls.Add(this.label5);
            this.tabPage1.Controls.Add(this.label4);
            this.tabPage1.Controls.Add(this.mainComboBox);
            this.tabPage1.Controls.Add(this.label3);
            this.tabPage1.Controls.Add(this.label2);
            this.tabPage1.Controls.Add(this.label1);
            this.tabPage1.Controls.Add(this.viceComboBox);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(431, 281);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "自动涅槃";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(276, 118);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 20;
            this.label6.Text = "进化道具";
            // 
            // jhPropComBox
            // 
            this.jhPropComBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.jhPropComBox.FormattingEnabled = true;
            this.jhPropComBox.Items.AddRange(new object[] {
            "玉露结晶",
            "天仙玉露",
            "强化丹A",
            "强化丹B"});
            this.jhPropComBox.Location = new System.Drawing.Point(261, 134);
            this.jhPropComBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.jhPropComBox.Name = "jhPropComBox";
            this.jhPropComBox.Size = new System.Drawing.Size(100, 20);
            this.jhPropComBox.TabIndex = 1007;
            // 
            // timesTextBox
            // 
            this.timesTextBox.Location = new System.Drawing.Point(341, 170);
            this.timesTextBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.timesTextBox.MaxLength = 4;
            this.timesTextBox.Name = "timesTextBox";
            this.timesTextBox.Size = new System.Drawing.Size(50, 21);
            this.timesTextBox.TabIndex = 1006;
            this.timesTextBox.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.timesTextBox_KeyPress);
            // 
            // viceLvTextBox
            // 
            this.viceLvTextBox.Location = new System.Drawing.Point(231, 170);
            this.viceLvTextBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.viceLvTextBox.MaxLength = 3;
            this.viceLvTextBox.Name = "viceLvTextBox";
            this.viceLvTextBox.Size = new System.Drawing.Size(50, 21);
            this.viceLvTextBox.TabIndex = 1005;
            this.viceLvTextBox.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.vicelvTextBox_KeyPress);
            // 
            // mainLvTextBox
            // 
            this.mainLvTextBox.Location = new System.Drawing.Point(120, 170);
            this.mainLvTextBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.mainLvTextBox.MaxLength = 3;
            this.mainLvTextBox.Name = "mainLvTextBox";
            this.mainLvTextBox.Size = new System.Drawing.Size(50, 21);
            this.mainLvTextBox.TabIndex = 1004;
            this.mainLvTextBox.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.mainlvTextBox_KeyPress);
            // 
            // propComBox2
            // 
            this.propComBox2.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.propComBox2.FormattingEnabled = true;
            this.propComBox2.Items.AddRange(new object[] {
            "",
            "涅槃丹",
            "未提炼的神丹",
            "天赐神魂",
            "凤凰羽毛",
            "七彩蘑菇"});
            this.propComBox2.Location = new System.Drawing.Point(242, 86);
            this.propComBox2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.propComBox2.Name = "propComBox2";
            this.propComBox2.Size = new System.Drawing.Size(121, 20);
            this.propComBox2.TabIndex = 1003;
            // 
            // propComBox1
            // 
            this.propComBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.propComBox1.FormattingEnabled = true;
            this.propComBox1.Items.AddRange(new object[] {
            "涅槃丹",
            "未提炼的神丹",
            "天赐神魂",
            "凤凰羽毛",
            "七彩蘑菇"});
            this.propComBox1.Location = new System.Drawing.Point(75, 84);
            this.propComBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.propComBox1.Name = "propComBox1";
            this.propComBox1.Size = new System.Drawing.Size(121, 20);
            this.propComBox1.TabIndex = 1002;
            this.propComBox1.SelectedIndexChanged += new System.EventHandler(this.propComBox1_SelectedIndexChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(274, 64);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(59, 12);
            this.label11.TabIndex = 14;
            this.label11.Text = "涅槃道具2";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(110, 64);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(59, 12);
            this.label10.TabIndex = 13;
            this.label10.Text = "涅槃道具1";
            // 
            // label9
            // 
            this.label9.ForeColor = System.Drawing.Color.Red;
            this.label9.Location = new System.Drawing.Point(15, 250);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(400, 12);
            this.label9.TabIndex = 12;
            this.label9.Text = "当前进度：玩家正在调整选项。";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // button1
            // 
            this.button1.Font = new System.Drawing.Font("楷体", 12.5F);
            this.button1.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.button1.Location = new System.Drawing.Point(170, 202);
            this.button1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(94, 38);
            this.button1.TabIndex = 11;
            this.button1.Text = "开始连涅";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(201, 11);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(29, 12);
            this.label5.TabIndex = 8;
            this.label5.Text = "主宠";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(111, 118);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 1;
            this.label4.Text = "副宠道具";
            // 
            // mainComboBox
            // 
            this.mainComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.mainComboBox.FormattingEnabled = true;
            this.mainComboBox.Location = new System.Drawing.Point(74, 34);
            this.mainComboBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.mainComboBox.Name = "mainComboBox";
            this.mainComboBox.Size = new System.Drawing.Size(290, 20);
            this.mainComboBox.TabIndex = 1000;
            this.mainComboBox.SelectedIndexChanged += new System.EventHandler(this.mainComboBox_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(173, 176);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "副宠等级：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(62, 176);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "主宠等级：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(283, 176);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "连涅次数：";
            // 
            // viceComboBox
            // 
            this.viceComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.viceComboBox.FormattingEnabled = true;
            this.viceComboBox.Items.AddRange(new object[] {
            "小神龙琅琊之卵-25cc",
            "小神龙琅琊之卵-60cc",
            "年兽宝宝之卵-200cc",
            "Mask之卵",
            "≮Mask≯之卵",
            "☆残酷☆之卵",
            "≮☆残酷☆≯之卵",
            "雅蠛蝶之卵",
            "北冥鸽之卵",
            "★虎妞宝宝★之卵",
            "灵花妖之卵",
            "宝宝龙之卵",
            "五彩梦蝶之卵"});
            this.viceComboBox.Location = new System.Drawing.Point(72, 134);
            this.viceComboBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.viceComboBox.Name = "viceComboBox";
            this.viceComboBox.Size = new System.Drawing.Size(145, 20);
            this.viceComboBox.TabIndex = 1001;
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Controls.Add(this.tabPage7);
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(439, 307);
            this.tabControl1.TabIndex = 0;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPage2
            // 
            this.tabPage2.BackColor = System.Drawing.Color.Gainsboro;
            this.tabPage2.Controls.Add(this.button3);
            this.tabPage2.Controls.Add(this.button2);
            this.tabPage2.Controls.Add(this.label8);
            this.tabPage2.Controls.Add(this.checkedListBox1);
            this.tabPage2.Controls.Add(this.label7);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Size = new System.Drawing.Size(431, 281);
            this.tabPage2.TabIndex = 2;
            this.tabPage2.Text = "任务助手";
            // 
            // button3
            // 
            this.button3.Font = new System.Drawing.Font("楷体", 12F);
            this.button3.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.button3.Location = new System.Drawing.Point(273, 142);
            this.button3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(124, 56);
            this.button3.TabIndex = 6;
            this.button3.Text = "清空原有设定";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // button2
            // 
            this.button2.Font = new System.Drawing.Font("楷体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button2.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.button2.Location = new System.Drawing.Point(273, 58);
            this.button2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(124, 56);
            this.button2.TabIndex = 5;
            this.button2.Text = "设定所选任务";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(80, 14);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 2;
            this.label8.Text = "所有可选任务";
            // 
            // checkedListBox1
            // 
            this.checkedListBox1.FormattingEnabled = true;
            this.checkedListBox1.Location = new System.Drawing.Point(34, 38);
            this.checkedListBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.checkedListBox1.Name = "checkedListBox1";
            this.checkedListBox1.Size = new System.Drawing.Size(234, 148);
            this.checkedListBox1.TabIndex = 1;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.ForeColor = System.Drawing.Color.Red;
            this.label7.Location = new System.Drawing.Point(79, 244);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(275, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "注：最多选择5个任务，本次设置会覆盖上次设置。";
            // 
            // tabPage3
            // 
            this.tabPage3.BackColor = System.Drawing.Color.Gainsboro;
            this.tabPage3.Controls.Add(this.label14);
            this.tabPage3.Controls.Add(this.button4);
            this.tabPage3.Controls.Add(this.label13);
            this.tabPage3.Controls.Add(this.taskTimesTextBox);
            this.tabPage3.Controls.Add(this.label12);
            this.tabPage3.Controls.Add(this.taskcomboBox1);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage3.Size = new System.Drawing.Size(431, 281);
            this.tabPage3.TabIndex = 3;
            this.tabPage3.Text = "任务助手";
            // 
            // label14
            // 
            this.label14.ForeColor = System.Drawing.SystemColors.ControlText;
            this.label14.Location = new System.Drawing.Point(96, 218);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(238, 22);
            this.label14.TabIndex = 5;
            this.label14.Text = "状态：正在等待玩家调整设置。";
            this.label14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // button4
            // 
            this.button4.Font = new System.Drawing.Font("楷体", 14F);
            this.button4.ForeColor = System.Drawing.Color.Red;
            this.button4.Location = new System.Drawing.Point(111, 126);
            this.button4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(192, 58);
            this.button4.TabIndex = 4;
            this.button4.Text = "开始重复完成任务";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.label13.Location = new System.Drawing.Point(225, 38);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(173, 12);
            this.label13.TabIndex = 3;
            this.label13.Text = "请输入重复次数（最多1000次）";
            // 
            // taskTimesTextBox
            // 
            this.taskTimesTextBox.Location = new System.Drawing.Point(231, 70);
            this.taskTimesTextBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.taskTimesTextBox.MaxLength = 4;
            this.taskTimesTextBox.Name = "taskTimesTextBox";
            this.taskTimesTextBox.Size = new System.Drawing.Size(154, 21);
            this.taskTimesTextBox.TabIndex = 2;
            this.taskTimesTextBox.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.taskTimesTextBox_KeyPress);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.ForeColor = System.Drawing.SystemColors.MenuHighlight;
            this.label12.Location = new System.Drawing.Point(39, 38);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(149, 12);
            this.label12.TabIndex = 1;
            this.label12.Text = "请选择您要重复完成的任务";
            // 
            // taskcomboBox1
            // 
            this.taskcomboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.taskcomboBox1.FormattingEnabled = true;
            this.taskcomboBox1.Location = new System.Drawing.Point(26, 70);
            this.taskcomboBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.taskcomboBox1.Name = "taskcomboBox1";
            this.taskcomboBox1.Size = new System.Drawing.Size(190, 20);
            this.taskcomboBox1.TabIndex = 0;
            // 
            // tabPage4
            // 
            this.tabPage4.BackColor = System.Drawing.Color.Transparent;
            this.tabPage4.Controls.Add(this.check_DXC);
            this.tabPage4.Controls.Add(this.label19);
            this.tabPage4.Controls.Add(this.groupBox2);
            this.tabPage4.Controls.Add(this.groupBox1);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(431, 281);
            this.tabPage4.TabIndex = 4;
            this.tabPage4.Text = "自动战斗";
            // 
            // check_DXC
            // 
            this.check_DXC.AutoSize = true;
            this.check_DXC.Location = new System.Drawing.Point(342, 90);
            this.check_DXC.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.check_DXC.Name = "check_DXC";
            this.check_DXC.Size = new System.Drawing.Size(84, 16);
            this.check_DXC.TabIndex = 16;
            this.check_DXC.Text = "启用多线程";
            this.check_DXC.UseVisualStyleBackColor = true;
            this.check_DXC.Visible = false;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Font = new System.Drawing.Font("宋体", 10.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label19.ForeColor = System.Drawing.Color.Red;
            this.label19.Location = new System.Drawing.Point(20, 418);
            this.label19.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(217, 15);
            this.label19.TabIndex = 16;
            this.label19.Text = "任何骚操作引起的问题均不负责";
            this.label19.Click += new System.EventHandler(this.label19_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.tabControl2);
            this.groupBox2.Location = new System.Drawing.Point(8, 102);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox2.Size = new System.Drawing.Size(415, 314);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "信息统计";
            // 
            // tabControl2
            // 
            this.tabControl2.Controls.Add(this.tabPage5);
            this.tabControl2.Controls.Add(this.tabPage6);
            this.tabControl2.Location = new System.Drawing.Point(6, 22);
            this.tabControl2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabControl2.Name = "tabControl2";
            this.tabControl2.SelectedIndex = 0;
            this.tabControl2.Size = new System.Drawing.Size(396, 284);
            this.tabControl2.TabIndex = 9;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.AM_info);
            this.tabPage5.Location = new System.Drawing.Point(4, 22);
            this.tabPage5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage5.Size = new System.Drawing.Size(388, 258);
            this.tabPage5.TabIndex = 0;
            this.tabPage5.Text = "战斗信息";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // AM_info
            // 
            this.AM_info.Location = new System.Drawing.Point(5, 6);
            this.AM_info.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AM_info.Multiline = true;
            this.AM_info.Name = "AM_info";
            this.AM_info.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.AM_info.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.AM_info.Size = new System.Drawing.Size(378, 246);
            this.AM_info.TabIndex = 0;
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.groupBox5);
            this.tabPage6.Controls.Add(this.groupBox4);
            this.tabPage6.Controls.Add(this.groupBox3);
            this.tabPage6.Location = new System.Drawing.Point(4, 22);
            this.tabPage6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage6.Size = new System.Drawing.Size(388, 258);
            this.tabPage6.TabIndex = 1;
            this.tabPage6.Text = "战斗统计";
            this.tabPage6.UseVisualStyleBackColor = true;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.AM_EXP);
            this.groupBox5.Controls.Add(this.AM_yb);
            this.groupBox5.Controls.Add(this.AM_money);
            this.groupBox5.Location = new System.Drawing.Point(258, 134);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox5.Size = new System.Drawing.Size(124, 110);
            this.groupBox5.TabIndex = 3;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "货币/经验统计";
            // 
            // AM_EXP
            // 
            this.AM_EXP.AutoSize = true;
            this.AM_EXP.Location = new System.Drawing.Point(8, 80);
            this.AM_EXP.Name = "AM_EXP";
            this.AM_EXP.Size = new System.Drawing.Size(41, 12);
            this.AM_EXP.TabIndex = 2;
            this.AM_EXP.Text = "经验：";
            // 
            // AM_yb
            // 
            this.AM_yb.AutoSize = true;
            this.AM_yb.Location = new System.Drawing.Point(8, 52);
            this.AM_yb.Name = "AM_yb";
            this.AM_yb.Size = new System.Drawing.Size(41, 12);
            this.AM_yb.TabIndex = 1;
            this.AM_yb.Text = "元宝：";
            // 
            // AM_money
            // 
            this.AM_money.AutoSize = true;
            this.AM_money.Location = new System.Drawing.Point(8, 26);
            this.AM_money.Name = "AM_money";
            this.AM_money.Size = new System.Drawing.Size(41, 12);
            this.AM_money.TabIndex = 0;
            this.AM_money.Text = "金币：";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.AM_proplist);
            this.groupBox4.Location = new System.Drawing.Point(4, 6);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Size = new System.Drawing.Size(250, 246);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "掉落统计";
            // 
            // AM_proplist
            // 
            this.AM_proplist.AllowUserToAddRows = false;
            this.AM_proplist.AllowUserToDeleteRows = false;
            this.AM_proplist.AllowUserToOrderColumns = true;
            this.AM_proplist.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.AM_proplist.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.道具名字,
            this.数量});
            this.AM_proplist.Location = new System.Drawing.Point(4, 14);
            this.AM_proplist.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AM_proplist.Name = "AM_proplist";
            this.AM_proplist.ReadOnly = true;
            this.AM_proplist.RowHeadersVisible = false;
            this.AM_proplist.RowHeadersWidth = 51;
            this.AM_proplist.RowTemplate.Height = 23;
            this.AM_proplist.Size = new System.Drawing.Size(245, 222);
            this.AM_proplist.TabIndex = 1;
            // 
            // 道具名字
            // 
            this.道具名字.DataPropertyName = "key";
            this.道具名字.HeaderText = "道具名字";
            this.道具名字.MinimumWidth = 6;
            this.道具名字.Name = "道具名字";
            this.道具名字.ReadOnly = true;
            this.道具名字.Width = 140;
            // 
            // 数量
            // 
            this.数量.DataPropertyName = "value";
            this.数量.HeaderText = "数量";
            this.数量.MinimumWidth = 6;
            this.数量.Name = "数量";
            this.数量.ReadOnly = true;
            this.数量.Width = 80;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.AM_BOSS);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.label17);
            this.groupBox3.Controls.Add(this.AM_loopnum);
            this.groupBox3.Controls.Add(this.AM_battlenum);
            this.groupBox3.Location = new System.Drawing.Point(258, 6);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Size = new System.Drawing.Size(124, 124);
            this.groupBox3.TabIndex = 1;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "次数统计";
            // 
            // AM_BOSS
            // 
            this.AM_BOSS.AutoSize = true;
            this.AM_BOSS.Location = new System.Drawing.Point(6, 62);
            this.AM_BOSS.Name = "AM_BOSS";
            this.AM_BOSS.Size = new System.Drawing.Size(65, 12);
            this.AM_BOSS.TabIndex = 3;
            this.AM_BOSS.Text = "遭遇BOSS：";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(18, 105);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(53, 12);
            this.label18.TabIndex = 2;
            this.label18.Text = "无掉落：";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(18, 84);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(53, 12);
            this.label17.TabIndex = 1;
            this.label17.Text = "有掉落：";
            // 
            // AM_loopnum
            // 
            this.AM_loopnum.AutoSize = true;
            this.AM_loopnum.Location = new System.Drawing.Point(6, 41);
            this.AM_loopnum.Name = "AM_loopnum";
            this.AM_loopnum.Size = new System.Drawing.Size(65, 12);
            this.AM_loopnum.TabIndex = 0;
            this.AM_loopnum.Text = "循环次数：";
            // 
            // AM_battlenum
            // 
            this.AM_battlenum.AutoSize = true;
            this.AM_battlenum.Location = new System.Drawing.Point(6, 21);
            this.AM_battlenum.Name = "AM_battlenum";
            this.AM_battlenum.Size = new System.Drawing.Size(65, 12);
            this.AM_battlenum.TabIndex = 0;
            this.AM_battlenum.Text = "战斗次数：";
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.Color.Transparent;
            this.groupBox1.Controls.Add(this.linkLabel1);
            this.groupBox1.Controls.Add(this.AM_fornum);
            this.groupBox1.Controls.Add(this.AM_start);
            this.groupBox1.Controls.Add(this.AM_looplabel);
            this.groupBox1.Controls.Add(this.AM_petname);
            this.groupBox1.Controls.Add(this.AM_skill);
            this.groupBox1.Controls.Add(this.label16);
            this.groupBox1.Controls.Add(this.AM_map);
            this.groupBox1.Controls.Add(this.label15);
            this.groupBox1.Location = new System.Drawing.Point(8, 14);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Size = new System.Drawing.Size(415, 78);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "挂机设定";
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Location = new System.Drawing.Point(138, 50);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(41, 12);
            this.linkLabel1.TabIndex = 15;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "不循环";
            this.linkLabel1.Visible = false;
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // AM_fornum
            // 
            this.AM_fornum.Location = new System.Drawing.Point(74, 46);
            this.AM_fornum.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AM_fornum.Name = "AM_fornum";
            this.AM_fornum.Size = new System.Drawing.Size(62, 21);
            this.AM_fornum.TabIndex = 13;
            this.AM_fornum.Visible = false;
            // 
            // AM_start
            // 
            this.AM_start.Location = new System.Drawing.Point(350, 20);
            this.AM_start.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AM_start.Name = "AM_start";
            this.AM_start.Size = new System.Drawing.Size(56, 46);
            this.AM_start.TabIndex = 14;
            this.AM_start.Text = "开始";
            this.AM_start.UseVisualStyleBackColor = true;
            this.AM_start.Click += new System.EventHandler(this.AM_start_Click);
            // 
            // AM_looplabel
            // 
            this.AM_looplabel.AutoSize = true;
            this.AM_looplabel.Location = new System.Drawing.Point(9, 50);
            this.AM_looplabel.Name = "AM_looplabel";
            this.AM_looplabel.Size = new System.Drawing.Size(65, 12);
            this.AM_looplabel.TabIndex = 12;
            this.AM_looplabel.Text = "循环层数：";
            this.AM_looplabel.Visible = false;
            // 
            // AM_petname
            // 
            this.AM_petname.AutoSize = true;
            this.AM_petname.Location = new System.Drawing.Point(195, 22);
            this.AM_petname.Name = "AM_petname";
            this.AM_petname.Size = new System.Drawing.Size(77, 12);
            this.AM_petname.TabIndex = 11;
            this.AM_petname.Text = "出战宠物：无";
            // 
            // AM_skill
            // 
            this.AM_skill.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.AM_skill.FormattingEnabled = true;
            this.AM_skill.Location = new System.Drawing.Point(254, 46);
            this.AM_skill.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AM_skill.Name = "AM_skill";
            this.AM_skill.Size = new System.Drawing.Size(79, 20);
            this.AM_skill.TabIndex = 10;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(195, 50);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(65, 12);
            this.label16.TabIndex = 9;
            this.label16.Text = "使用技能：";
            // 
            // AM_map
            // 
            this.AM_map.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.AM_map.FormattingEnabled = true;
            this.AM_map.Location = new System.Drawing.Point(74, 18);
            this.AM_map.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.AM_map.Name = "AM_map";
            this.AM_map.Size = new System.Drawing.Size(110, 20);
            this.AM_map.TabIndex = 8;
            this.AM_map.SelectedIndexChanged += new System.EventHandler(this.AM_map_SelectedIndexChanged);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(9, 22);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(65, 12);
            this.label15.TabIndex = 7;
            this.label15.Text = "战斗地图：";
            // 
            // tabPage7
            // 
            this.tabPage7.BackColor = System.Drawing.Color.Transparent;
            this.tabPage7.Controls.Add(this.checkBox_FIGHTFAST);
            this.tabPage7.Controls.Add(this.checkBox_FIGHT_DEBUG);
            this.tabPage7.Controls.Add(this.panel_FIGHT_DEBUG);
            this.tabPage7.Location = new System.Drawing.Point(4, 22);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Padding = new System.Windows.Forms.Padding(3, 3, 3, 3);
            this.tabPage7.Size = new System.Drawing.Size(431, 281);
            this.tabPage7.TabIndex = 5;
            this.tabPage7.Text = "挂机设置";
            // 
            // checkBox_FIGHTFAST
            // 
            this.checkBox_FIGHTFAST.AutoSize = true;
            this.checkBox_FIGHTFAST.Location = new System.Drawing.Point(118, 15);
            this.checkBox_FIGHTFAST.Name = "checkBox_FIGHTFAST";
            this.checkBox_FIGHTFAST.Size = new System.Drawing.Size(96, 16);
            this.checkBox_FIGHTFAST.TabIndex = 5;
            this.checkBox_FIGHTFAST.Text = "挂机急速模式";
            this.checkBox_FIGHTFAST.UseVisualStyleBackColor = true;
            this.checkBox_FIGHTFAST.CheckedChanged += new System.EventHandler(this.checkBox_FIGHTFAST_CheckedChanged);
            // 
            // checkBox_FIGHT_DEBUG
            // 
            this.checkBox_FIGHT_DEBUG.AutoSize = true;
            this.checkBox_FIGHT_DEBUG.Location = new System.Drawing.Point(16, 15);
            this.checkBox_FIGHT_DEBUG.Name = "checkBox_FIGHT_DEBUG";
            this.checkBox_FIGHT_DEBUG.Size = new System.Drawing.Size(96, 16);
            this.checkBox_FIGHT_DEBUG.TabIndex = 4;
            this.checkBox_FIGHT_DEBUG.Text = "开启测试模式";
            this.checkBox_FIGHT_DEBUG.UseVisualStyleBackColor = true;
            this.checkBox_FIGHT_DEBUG.CheckedChanged += new System.EventHandler(this.checkBox_FIGHT_DEBUG_CheckedChanged);
            // 
            // panel_FIGHT_DEBUG
            // 
            this.panel_FIGHT_DEBUG.Controls.Add(this.textBox_BattleNum);
            this.panel_FIGHT_DEBUG.Controls.Add(this.label20);
            this.panel_FIGHT_DEBUG.Controls.Add(this.FIGHT_ATK);
            this.panel_FIGHT_DEBUG.Location = new System.Drawing.Point(16, 37);
            this.panel_FIGHT_DEBUG.Name = "panel_FIGHT_DEBUG";
            this.panel_FIGHT_DEBUG.Size = new System.Drawing.Size(224, 70);
            this.panel_FIGHT_DEBUG.TabIndex = 3;
            this.panel_FIGHT_DEBUG.Visible = false;
            // 
            // textBox_BattleNum
            // 
            this.textBox_BattleNum.Location = new System.Drawing.Point(103, 8);
            this.textBox_BattleNum.Name = "textBox_BattleNum";
            this.textBox_BattleNum.Size = new System.Drawing.Size(100, 21);
            this.textBox_BattleNum.TabIndex = 5;
            this.textBox_BattleNum.Text = "0";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(15, 13);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(89, 12);
            this.label20.TabIndex = 4;
            this.label20.Text = "目标怪物数量：";
            // 
            // FIGHT_ATK
            // 
            this.FIGHT_ATK.AutoSize = true;
            this.FIGHT_ATK.Location = new System.Drawing.Point(17, 35);
            this.FIGHT_ATK.Name = "FIGHT_ATK";
            this.FIGHT_ATK.Size = new System.Drawing.Size(72, 16);
            this.FIGHT_ATK.TabIndex = 3;
            this.FIGHT_ATK.Text = "秒杀怪物";
            this.FIGHT_ATK.UseVisualStyleBackColor = true;
            this.FIGHT_ATK.CheckedChanged += new System.EventHandler(this.FIGHT_ATK_CheckedChanged);
            // 
            // AutoMapTimer
            // 
            this.AutoMapTimer.Tick += new System.EventHandler(this.AutoMapTimer_Tick);
            // 
            // PlayerHelper
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.ClientSize = new System.Drawing.Size(441, 316);
            this.Controls.Add(this.tabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.MaximizeBox = false;
            this.Name = "PlayerHelper";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "玩家助手";
            this.TopMost = true;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.PlayerHelper_FormClosing);
            this.Load += new System.EventHandler(this.PlayerHelper_Load);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.tabPage4.ResumeLayout(false);
            this.tabPage4.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.tabControl2.ResumeLayout(false);
            this.tabPage5.ResumeLayout(false);
            this.tabPage5.PerformLayout();
            this.tabPage6.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.AM_proplist)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage7.ResumeLayout(false);
            this.tabPage7.PerformLayout();
            this.panel_FIGHT_DEBUG.ResumeLayout(false);
            this.panel_FIGHT_DEBUG.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TextBox timesTextBox;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.ComboBox viceComboBox;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox mainLvTextBox;
        private System.Windows.Forms.TextBox viceLvTextBox;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox mainComboBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.ComboBox propComBox2;
        private System.Windows.Forms.ComboBox propComBox1;
        private System.Windows.Forms.ComboBox jhPropComBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckedListBox checkedListBox1;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.ComboBox taskcomboBox1;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox taskTimesTextBox;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label AM_loopnum;
        private System.Windows.Forms.Label AM_battlenum;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox AM_fornum;
        private System.Windows.Forms.Label AM_looplabel;
        private System.Windows.Forms.Label AM_petname;
        private System.Windows.Forms.ComboBox AM_skill;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.ComboBox AM_map;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TabControl tabControl2;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.TextBox AM_info;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.Button AM_start;
        private System.Windows.Forms.Timer AutoMapTimer;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.DataGridView AM_proplist;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.DataGridViewTextBoxColumn 道具名字;
        private System.Windows.Forms.DataGridViewTextBoxColumn 数量;
        private System.Windows.Forms.LinkLabel linkLabel1;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Label AM_EXP;
        private System.Windows.Forms.Label AM_yb;
        private System.Windows.Forms.Label AM_money;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.CheckBox check_DXC;
        private System.Windows.Forms.Label AM_BOSS;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.CheckBox checkBox_FIGHT_DEBUG;
        private System.Windows.Forms.Panel panel_FIGHT_DEBUG;
        private System.Windows.Forms.TextBox textBox_BattleNum;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.CheckBox FIGHT_ATK;
        private System.Windows.Forms.CheckBox checkBox_FIGHTFAST;
    }
}