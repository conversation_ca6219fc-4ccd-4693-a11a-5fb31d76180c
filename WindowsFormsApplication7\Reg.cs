﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    public partial class Reg : Form
    {
        public Reg()
        {
            InitializeComponent();
        }

        internal static string[] SpecialChar = { "<", ">", "|", ",", ".", "?" };
 

        private void button1_Click(object sender, EventArgs e)
        {
            if (File.Exists(DataProcess.ProgramPath+@"\PageMain\Main.dat"))
            {
                SkTools.AutoClosedMsgBox.Show(Res.RM.GetString("重复注册提示"), Res.RM.GetString("提示"),2000);
                Tools.ForcedExit("重复注册");
            }

            if (!new DataProcess().NameRulerMsg(textBox1.Text))
            {
                return;
            }

            UserInfo user = new UserInfo()
            {
                名字 = textBox1.Text,
                主宠物 = 1.ToString(),
                宠物1 = 1.ToString(),
                元宝 = DataProcess.GetInt("5BED90").ToString(),//100
                金币 = DataProcess.GetInt("52ED908C9DAEAE").ToString(),//8000000
                水晶 = DataProcess.GetInt("58ED908C").ToString(),//2000
                自动战斗次数 = DataProcess.GetInt("5BED908C9D").ToString(),//10000
                道具容量 = DataProcess.GetInt("5BEF90").ToString(),
                牧场容量= DataProcess.GetInt("52ED").ToString(),
                vip = DataProcess.GetInt("5A").ToString(),
                时之券 = DataProcess.GetInt("5A").ToString(),
                VIP积分 = DataProcess.GetInt("5A").ToString(),
                威望 = DataProcess.GetInt("5A").ToString(),
                刷怪数 = DataProcess.GetInt("5A").ToString(),
                每日刷怪数 = DataProcess.GetInt("5A").ToString(),
                条款 = "Agree",
                NB1 = DataProcess.GetDiskInfo(),
                NB2 = string.Empty,
                NB3 = string.Empty,
                版本号 = DataProcess.Version,
                小版本号 = DataProcess.Version2,
                注册时间 = DateTime.Now.ToString(),
                SID = Guid.NewGuid().ToString("N")
            };
            if (user.NB1 == "qsdashuaibi")
            {
                SkTools.AutoClosedMsgBox.Show("当前可能在云端注册,请找青衫绑定云端。如果不是请勿在虚拟机中注册!", Res.RM.GetString("提示"), 2000);
                Tools.ForcedExit("可能在云端注册。");
            }
            new DataProcess().SaveUserDataFile(user);

            PetInfo pet = new PetInfo();
            List<PetConfig> types = new DataProcess().GetSPList();
            PetConfig type = types[DataProcess.RandomGenerator.Next(0, types.Count)];
            pet.宠物名字 = type.宠物名字;
            pet.形象 = type.宠物序号;
            pet.五行 = type.系别;
            pet.当前经验 = 1.ToString();
            pet.宠物序号 = 1.ToString();
            pet = new DataProcess().SetDefaultAttribute(pet);
            new DataProcess().AddPet(pet);

            PropInfo info1 = new PropInfo
            {
                道具类型ID = DataProcess.GetInt("58ED918A9DA7AC272096").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = DataProcess.GetInt("5BEF90").ToString()
            };
            //120个经验签
            new DataProcess().AddPlayerProp(info1);

            PropInfo info2 = new PropInfo
            {
                道具类型ID = DataProcess.GetInt("58ED918A9CAEAF292092").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = NumEncrypt.二十().ToString()
            };
            //20个涅槃兽礼包
            new DataProcess().AddPlayerProp(info2);

            PropInfo info3 = new PropInfo
            {
                道具类型ID = DataProcess.GetInt("58ED918B9DA8AF222095").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = NumEncrypt.一().ToString()
            };
            //1个萌新礼券
            new DataProcess().AddPlayerProp(info3);

            PropInfo info4 = new PropInfo
            {
                道具类型ID = DataProcess.GetInt("58ED918A9DA7AC292095").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = NumEncrypt.一百().ToString()
            };
            //100个捕捉球大礼包
            new DataProcess().AddPlayerProp(info4);

            PropInfo info5 = new PropInfo
            {
                道具类型ID = DataProcess.GetInt("58ED918A9DA7AC262091").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = NumEncrypt.二十().ToString()
            };
            //20个涅槃兽礼包
            new DataProcess().AddPlayerProp(info5);

            PropInfo info6 = new PropInfo
            {
                道具类型ID = DataProcess.GetInt("58ED91849DACAF272095").ToString(),
                道具位置 = PropLoaction.背包.ToString(),
                道具数量 = "1"
            };
            //一套创世
            new DataProcess().AddPlayerProp(info6);

            Close();
        }
    }
}
