﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Shikong.Pokemon2.PCG
{
    public partial class userSet : Form
    {
        public userSet()
        {
            InitializeComponent();
        }

        private void gj_type_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (gj_type.Text != "地狱之门" && gj_type.Text != "通天塔")
            {
                gj_num2.Visible = false;
                gj_num.Items.Clear();
                gj_num.DropDownStyle = ComboBoxStyle.DropDownList;
                String[] temp = str.Split('|');
                foreach (var t in temp)
                {
                    String[] tempINI = t.Split(',');
                    if(tempINI[0]== gj_type.Text)
                    {
                        for(int i = 1; i <= Convert.ToInt16(tempINI[3]);i++)
                        {
                            gj_num.Items.Add(i);
                        }
                    }
                }
                gj_num.MaxDropDownItems = 8;
            }
            if (gj_type.Text == "地狱之门")
            {
                gj_num2.Visible = true;
                gj_num.Items.Clear();
                gj_num.DropDownStyle = ComboBoxStyle.Simple;
                gj_num.MaxLength = 6;
            }
            if (gj_type.Text == "通天塔")
            {
                gj_num2.Visible = false;
                gj_num.Items.Clear();
                gj_num.DropDownStyle = ComboBoxStyle.Simple;
                gj_num.MaxLength = 3;
            }

        }
        String str = "伊苏王的神墓,9001,2016101801,22|火龙王的宫殿,9002,2016120304,30|" +
                "史芬克斯密穴,9003,943015,27|玲珑城,9004,2017070208,30|BOSS集中营,9010,2018102701,24|" +
                "楼兰古城的遗迹,9011,2018102702,50|阿尔提密林,9012,2019030312,50";
        private void userSet_Load(object sender, EventArgs e)
        {
            showInfo();
            //设置挂机信息
            gj_type.Items.Clear();
            String[] temp = str.Split('|');
            foreach (var t in temp)
            {
                String[] tempINI = t.Split(',');
                gj_type.Items.Add(tempINI[0]);
            }
            gj_type.Items.Add("地狱之门");
            gj_type.Items.Add("通天塔");
        }
        public void showInfo()
        {
            //基础
            comboBox2.Text = "水晶";
            valueLabel.Text = "元宝数量：" + new DataProcess().ReadUserInfo().元宝;
            //获取基础信息
            var u = new DataProcess().ReadUserInfo();
            VIP.Text = "VIP：" + (u.星辰VIP ? "星辰VIP" : u.至尊VIP ? "至尊VIP" : "无");
            JB.Text = "金币上限：" + ((new DataProcess().getMaxValue(1) + new DataProcess().ghb(5)) / 100000000) + "亿";
            YB.Text = "元宝上限：" + ((new DataProcess().getMaxValue(2) + new DataProcess().ghb(7)) / 10000) + "万";
            SJ.Text = "水晶上限：" + ((new DataProcess().getMaxValue(2) + new DataProcess().ghb(8)) / 10000) + "万";
            HL1.Text = "赫拉神殿小怪伤害：" + (u.星辰VIP ? 2 + new DataProcess().ggj(7) : u.至尊VIP ? 2 : 1);
            HL2.Text = "赫拉神殿BOSS伤害：" + (u.星辰VIP ? 1+8 + new DataProcess().ggj(2) : u.至尊VIP ? 5 + new DataProcess().ggj(2) : 1);
            BOSS.Text = "必遇BOSS次数减少：" + new DataProcess().gbs();
            ZHJY.Text = "转换战斗胜利经验：" + new DataProcess().gjy() * 100 + "%";
            other.Text = "批量开包:" + (new DataProcess().gkb() ? "已激活" : "未激活");
            niepan.Text = "自动涅槃减少：" + new DataProcess().gnt() + "s["+(12-new DataProcess().gnt()) +"s]";
            if (Fight.AutoMap)//自动副本
            {
                String[] temp = str.Split('|');
                foreach (var t in temp)
                {
                    String[] tempINI = t.Split(',');
                    if (tempINI[1] == Fight.地图)
                    {
                        GJZT.Text = "挂机状态："+ tempINI[0]+"["+ Fight.MapFloor + "]";
                        break;
                    }
                }
                
            }
            else if (Fight.AutoTT)//自动通天
            {
                GJZT.Text = "挂机状态：通天塔 - " + Fight.TTFloor + "层";
            }
            else if (Fight.AutoHell)//自动地狱
            {
                GJZT.Text = $"挂机状态：地狱 - {Fight.HellFloor / 10 + 1}[{(Fight.HellFloor % 10 == 0 ? 10 : Fight.HellFloor % 10)}]";//挂机状态：地狱 - 1001[7]
            }
            else
            {
                GJZT.Text = "挂机状态：空闲";
            }
            //设置
            name.Text = u.名字;
            NOBOSS.Checked = DataProcess.Noboss;
            JBYC.Checked = !DataProcess.GOLDMAX;
            //青衫专属
            if (u.论坛ID == "青衫")
            {
                showAllTask.Visible = true;
                debug_onlineTask.Visible = true;
                showAllTask.Checked = DataProcess.TD;
                debug_onlineTask.Checked = DataProcess.isLocalOnlineTask;
            }
        }
        private void button1_Click(object sender, EventArgs e)
        {
            if (gj_type.Text == "")
            {
                MessageBox.Show("还没选择挂机地图!");
                return;
            }
            var isNum = Regex.IsMatch(gj_num.Text, @"^[1-9]\d*$");
            if (!isNum)
            {
                MessageBox.Show("请输入大于0的整数!");
                return;
            }
            if (gj_type.Text != "地狱之门" && gj_type.Text != "通天塔")
            {
                if (gj_num.Text == "")
                {
                    MessageBox.Show("请选择层数!");
                    return;
                }
                String[] temp = str.Split('|');
                foreach (var t in temp)
                {
                    String[] tempINI = t.Split(',');
                    if (tempINI[0] == gj_type.Text)
                    {
                        Fight.地图 = tempINI[1];
                        break;
                    }
                }
                Fight.AutoMap = true;//自动副本
                Fight.AutoHell = false;
                Fight.AutoTT = false;
                Fight.MapFloor = Convert.ToInt32(gj_num.Text);
            }
            if (gj_type.Text == "地狱之门")
            {
                if (gj_num.Text == "" || gj_num2.Text == "")
                {
                    MessageBox.Show("请选择层数!");
                    return;
                }
                if (Convert.ToInt32(gj_num.Text)<1 || Convert.ToInt32(gj_num.Text) > 60000)
                {
                    MessageBox.Show("大层数最高为1-60000层！");
                    return;
                }
                PropInfo dykey = new DataProcess().GetAP_ID("2016101705");
                if (dykey == null || Convert.ToInt32(dykey.道具数量) < 1)
                {
                    MessageBox.Show("地狱钥匙不足，无法开启自动地狱！");
                    return;
                }
                Fight.HellFloor = (Convert.ToInt32(gj_num.Text) - 1) * 10 + Convert.ToInt32(gj_num2.Text);
                Fight.AutoHell = true;//自动地狱
                Fight.AutoMap = false;
                Fight.AutoTT = false;
            }
            if (gj_type.Text == "通天塔")
            {
                if (gj_num.Text == "")
                {
                    MessageBox.Show("请选择层数!");
                    return;
                }
                if (Convert.ToInt32(gj_num.Text)<1 || Convert.ToInt32(gj_num.Text)>500)
                {
                    MessageBox.Show("请设置1-500以内的范围！");
                    return;
                }
                PropInfo ttkey = new DataProcess().GetAP_ID("2018021701");
                if (ttkey == null || Convert.ToInt32(ttkey.道具数量) < 1)
                {
                    MessageBox.Show("通天钥匙不足，无法开启自动通天！");
                    return;
                }
                Fight.AutoTT = true;//自动通天
                Fight.AutoMap = false;
                Fight.AutoHell = false;
                Fight.TTFloor = Convert.ToInt32(gj_num.Text);
            }
            showInfo();
            MessageBox.Show("设置完成,需要去指定地图挂机,本设置窗口并不能自动战斗。");
            Close();
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (Convert.ToInt16(comboBox1.Text) >= 1 && Convert.ToInt16(comboBox1.Text)<=6)
            {
                string path = @"PageMain\Content\Img\player\photos\" + comboBox1.Text + ".gif";

                if (!File.Exists(path))
                {
                    if (!File.Exists(path))
                    {
                        path = @"PageMain\Content\Img\player\main.gif";
                    }
                }
                pictureBox1.Image = Image.FromFile(path);
                File.Copy(@"PageMain/Content/Img/player/photos/" + Convert.ToInt16(comboBox1.Text) + ".gif",
                            @"PageMain/Content/Img/player/main.gif", true);
                File.Copy(@"PageMain/Content/player/photos/" + Convert.ToInt16(comboBox1.Text) + ".gif",
                    @"PageMain/Content/player/main.gif", true);
                UserInfo user = new DataProcess().ReadUserInfo();
                user.sex = Convert.ToInt16(comboBox1.Text) >= 1 && Convert.ToInt16(comboBox1.Text) <= 3 ? "帅哥" : "美女";
                new DataProcess().SaveUserDataFile(user);
                return;
            }
            
        }

        private void changeName_Click(object sender, EventArgs e)
        {
            UserInfo user = new DataProcess().ReadUserInfo();
            user.名字 = name.Text;
            new DataProcess().SaveUserDataFile(user);
            MessageBox.Show("改名成功!");
        }

        private void NOBOSS_CheckedChanged(object sender, EventArgs e)
        {
            DataProcess.Noboss = NOBOSS.Checked;
        }

        private void JBYC_CheckedChanged(object sender, EventArgs e)
        {
            DataProcess.GOLDMAX = !JBYC.Checked;
        }

        private void stopgj_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Fight.AutoMap = false;
            Fight.AutoHell = false;
            Fight.AutoTT = false;
            GJZT.Text = "挂机状态：空闲";
            MessageBox.Show("已重置挂机参数!");
        }

        private void showAllTask_CheckedChanged(object sender, EventArgs e)
        {
            DataProcess.TD = showAllTask.Checked;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if(!new DataProcess().ReadUserInfo().星辰VIP)
            {
                MessageBox.Show("此功能星辰VIP可用!", "货币转换");
                return;
            }
            var isNum = Regex.IsMatch(valueNum.Text, @"^[1-9]\d*$");
            if (!isNum)
            {
                MessageBox.Show("请输入大于0的整数!", "宰了你");
                return;
            }
            if (Convert.ToInt32(valueNum.Text) > 100000000)
            {
                MessageBox.Show("数值过大!", "宰了你");
                return;
            }
            var user = new DataProcess().ReadUserInfo();
            if (comboBox2.Text == "水晶")
            {
                if(Convert.ToInt32(user.元宝) < Convert.ToInt32(valueNum.Text))
                {
                    MessageBox.Show("元宝不足!");
                    return;
                }
                user.元宝 = (Convert.ToInt32(user.元宝) - Convert.ToInt32(valueNum.Text)).ToString();
                user.水晶 = (Convert.ToInt32(valueNum.Text) + Convert.ToInt32(user.水晶)).ToString();
            }
            if (comboBox2.Text == "元宝")
            {
                if (Convert.ToInt32(user.水晶) < Convert.ToInt32(valueNum.Text))
                {
                    MessageBox.Show("水晶不足!");
                    return;
                }
                user.水晶 = (Convert.ToInt32(user.水晶) - Convert.ToInt32(valueNum.Text)).ToString();
                user.元宝 = (Convert.ToInt32(valueNum.Text) + Convert.ToInt32(user.元宝)).ToString();
            }

            new DataProcess().SaveUserDataFile(user);
            setValueLabel();
            MessageBox.Show($"转换完成!\r\n当前元宝:{user.元宝}\r\n当前水晶:{user.水晶}");
        }
        void setValueLabel()
        {
            if (comboBox2.Text == "水晶")
            {
                valueLabel.Text = "元宝数量：" + new DataProcess().ReadUserInfo().元宝;
            }
            if (comboBox2.Text == "元宝")
            {
                valueLabel.Text = "水晶数量：" + new DataProcess().ReadUserInfo().水晶;
            }
        }
        private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {
            setValueLabel();
        }

        private void debug_onlineTask_CheckedChanged(object sender, EventArgs e)
        {
            DataProcess.isLocalOnlineTask = debug_onlineTask.Checked;
        }
    }
}
