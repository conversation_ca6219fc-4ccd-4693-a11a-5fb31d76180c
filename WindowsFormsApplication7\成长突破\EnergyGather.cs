using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using Newtonsoft.Json;
using PetShikongTools;
using System.Windows.Forms;

namespace Shikong.Pokemon2.PCG.成长突破
{
    /// <summary>
    /// 能量汇聚功能 - 管理神圣结界等级
    /// </summary>
    public class EnergyGather
    {
        /// <summary>
        /// 神圣结界的最大等级
        /// </summary>
        public const int MAX_LEVEL = 100;

        /// <summary>
        /// 聚灵晶石的道具ID
        /// </summary>
        public const string JULING_ITEM_ID = "2025060101"; // 需要确认实际道具ID

        /// <summary>
        /// 经验配置文件路径
        /// </summary>
        private const string EXP_CONFIG_PATH = @"PageMain\成长突破\EnergyGatherExp.config";

        /// <summary>
        /// 缓存的经验配置数据
        /// </summary>
        private static Dictionary<int, int> _expConfigCache = null;

        /// <summary>
        /// 经验配置类
        /// </summary>
        private class ExpConfig
        {
            public int level { get; set; }
            public int exp { get; set; }
        }

        /// <summary>
        /// 加载经验配置
        /// </summary>
        private static void LoadExpConfig()
        {
            if (_expConfigCache != null) return;

            try
            {
                string fullPath = Path.Combine(DataProcess.ProgramPath, EXP_CONFIG_PATH);
                
                // 如果配置文件不存在，使用默认公式生成
                if (!File.Exists(fullPath))
                {
                    _expConfigCache = new Dictionary<int, int>();
                    for (int i = 0; i < MAX_LEVEL; i++)
                    {
                        _expConfigCache[i] = 100 + i * 100;
                    }
                    return;
                }

                // 读取加密的配置文件
                string encryptedData = File.ReadAllText(fullPath);
                
                // 解密数据
                string json = SkRC4.DES.DecryptRC4(encryptedData, new DataProcess().GetKey(1));
                
                // 反序列化
                var expList = JsonConvert.DeserializeObject<List<ExpConfig>>(json);
                
                // 转换为字典缓存
                _expConfigCache = new Dictionary<int, int>();
                foreach (var config in expList)
                {
                    _expConfigCache[config.level] = config.exp;
                }
            }
            catch (Exception ex)
            {
                // 出错时使用默认公式
                _expConfigCache = new Dictionary<int, int>();
                for (int i = 0; i < MAX_LEVEL; i++)
                {
                    _expConfigCache[i] = 100 + i * 100;
                }
            }
        }

        /// <summary>
        /// 获取用户当前的神圣结界等级
        /// </summary>
        public static int GetBarrierLevel(UserInfo user)
        {
            if (user.神圣结界等级 == null || string.IsNullOrEmpty(user.神圣结界等级))
            {
                return 0;
            }
            return Convert.ToInt32(user.神圣结界等级);
        }

        /// <summary>
        /// 获取升级到下一级所需的经验值
        /// </summary>
        public static int GetUpgradeExp(int currentLevel)
        {
            if (currentLevel >= MAX_LEVEL) return 0;
            
            // 确保配置已加载
            LoadExpConfig();

            // 从配置中获取经验值
            if (_expConfigCache != null && _expConfigCache.ContainsKey(currentLevel))
            {
                return _expConfigCache[currentLevel];
            }
            
            // 如果找不到配置，使用默认公式
            return 100 + currentLevel * 100;
        }

        /// <summary>
        /// 使用聚灵晶石增加经验
        /// </summary>
        /// <param name="useCount">使用数量</param>
        /// <returns>操作结果消息</returns>
        public static string UseJulingStone(int useCount)
        {
            try
            {
                var dataProcess = new DataProcess();
                var user = dataProcess.ReadUserInfo();
                
                // 检查道具数量
                var prop = dataProcess.GetAP_ID(JULING_ITEM_ID);
                if (prop == null || Convert.ToInt32(prop.道具数量) < useCount)
                {
                    return "聚灵晶石数量不足！";
                }

                // 获取当前等级和经验
                int currentLevel = GetBarrierLevel(user);
                int currentExp = user.神圣结界经验 == null ? 0 : Convert.ToInt32(user.神圣结界经验);

                if (currentLevel >= MAX_LEVEL)
                {
                    return "神圣结界已达到最高等级！";
                }

                // 计算获得的经验（每个聚灵晶石给予1-30点经验）
                int totalExp = 0;
                for (int i = 0; i < useCount; i++)
                {
                    totalExp += DataProcess.RandomGenerator.Next(1, 31);
                }
                
                int newExp = currentExp + totalExp;

                // 计算升级
                int newLevel = currentLevel;
                int remainingExp = newExp;
                
                while (newLevel < MAX_LEVEL)
                {
                    int needExp = GetUpgradeExp(newLevel);
                    if (remainingExp >= needExp)
                    {
                        remainingExp -= needExp;
                        newLevel++;
                    }
                    else
                    {
                        break;
                    }
                }

                // 更新用户数据
                user.神圣结界等级 = newLevel.ToString();
                user.神圣结界经验 = remainingExp.ToString();

                // 消耗道具
                dataProcess.ReviseOrDeletePP(prop, useCount);

                // 保存数据
                dataProcess.SaveUserDataFile(user);

                string result = $"成功使用{useCount}个聚灵晶石，获得{totalExp}点经验！";
                if (newLevel > currentLevel)
                {
                    result += $"\n神圣结界等级提升至Lv.{newLevel}！";
                }

                return result;
            }
            catch (Exception ex)
            {
                return "使用失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 获取当前能量汇聚状态信息
        /// </summary>
        public static Dictionary<string, string> GetEnergyGatherInfo()
        {
            var user = new DataProcess().ReadUserInfo();
            int currentLevel = GetBarrierLevel(user);
            int currentExp = user.神圣结界经验 == null ? 0 : Convert.ToInt32(user.神圣结界经验);
            int needExp = GetUpgradeExp(currentLevel);
            
            // 获取聚灵晶石数量
            var prop = new DataProcess().GetAP_ID(JULING_ITEM_ID);
            int julingStoneCount = prop == null ? 0 : Convert.ToInt32(prop.道具数量);

            var info = new Dictionary<string, string>
            {
                ["currentLevel"] = currentLevel.ToString(),
                ["currentExp"] = currentExp.ToString(),
                ["needExp"] = needExp.ToString(),
                ["maxLevel"] = MAX_LEVEL.ToString(),
                ["progress"] = currentLevel >= MAX_LEVEL ? "100" : 
                              ((currentExp * 100.0 / needExp).ToString("F1")),
                ["julingStoneCount"] = julingStoneCount.ToString()
            };

            return info;
        }

        /// <summary>
        /// 重新加载配置（用于配置更新后）
        /// </summary>
        public static void ReloadConfig()
        {
            _expConfigCache = null;
            LoadExpConfig();
        }
    }
} 