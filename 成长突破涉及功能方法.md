根据 `sksd.html` 的前端代码，我来分析成长突破功能从前端到后端涉及的所有函数：
Search files...
Search files...
根据对前端代码和后端实现的分析，我来总结成长突破功能从前端到后端涉及的所有函数：

## 成长突破功能函数调用链分析

### 🎯 前端 JavaScript 函数（sksd.html）

#### 1. 界面控制函数
- `showPopup(popupId)` - 显示弹框（能量汇聚、成长突破等）
- `hidePopup()` - 隐藏弹框
- `tupoBtnDown(img)` / `tupoBtnUp(img)` - 按钮点击效果

#### 2. 成长突破核心前端函数
- `updateBreakthroughInfo()` - **核心函数**：更新成长突破信息显示
- `updatePhoenixStoneDisplay()` - 更新凤凰晶石显示和成功率计算
- `useBreakthroughStone()` - 使用突破圣石（实际只是检查）
- `usePhoenixStone()` - 选择凤凰晶石数量
- `executeBreakthrough()` - **关键函数**：执行成长突破操作

#### 3. 能量汇聚前端函数
- `updateEnergyGatherInfo()` - 更新能量汇聚信息显示
- `useJulingStone()` - 使用聚灵晶石

---

### 🔗 前端调用后端接口

#### 1. 成长突破接口调用
```javascript
// 前端 → 后端接口
window.external.GrowthBreakthrough_GetInfo()           // 获取突破信息
window.external.GrowthBreakthrough_TryBreakthrough(phoenixStoneCount)  // 执行突破
```

#### 2. 能量汇聚接口调用
```javascript
// 前端 → 后端接口
window.external.EnergyGather_GetInfo()                 // 获取能量汇聚信息
window.external.EnergyGather_UseJulingStone(count)     // 使用聚灵晶石
```

---

### 🖥️ 后端 Form1.cs 接口函数

#### 1. 成长突破接口方法
- `GrowthBreakthrough_GetInfo()` - 获取突破信息（返回JSON）
- `GrowthBreakthrough_GetInfo(string petId)` - 获取指定宠物突破信息
- `GrowthBreakthrough_TryBreakthrough(int phoenixStoneCount)` - 执行突破
- `GrowthBreakthrough_TryBreakthrough(string petId, int phoenixStoneCount)` - 指定宠物突破
- `GrowthBreakthrough_GetAllConfigs()` - 获取所有突破配置
- `GrowthBreakthrough_GetAllPetsSummary()` - 获取所有宠物突破摘要

#### 2. 能量汇聚接口方法
- `EnergyGather_GetInfo()` - 获取能量汇聚信息（返回JSON）
- `EnergyGather_UseJulingStone(string count)` - 使用聚灵晶石

#### 3. 命令处理方法
- `ProcessEnergyGatherCommand(string command)` - 处理能量汇聚命令
- `ProcessGrowthBreakthroughCommand(string command)` - 处理成长突破命令（在recv函数中调用）

---

### 🔧 后端核心业务逻辑类

#### 1. GrowthBreakthrough.cs 静态类
- `TryBreakthrough(string petId, int phoenixStoneCount)` - **核心突破逻辑**
- `GetBreakthroughInfo(string petId)` - 获取突破信息
- `GetAllConfigs()` - 获取所有配置
- `GetAllPetsBreakthroughSummary()` - 获取宠物摘要
- `ProcessCommand(string command)` - 处理命令行命令
- `CheckRaceRequirement(string petRace, string requiredRace)` - 检查种族要求

#### 2. EnergyGather.cs 静态类
- `UseJulingStone(int useCount)` - **核心使用聚灵晶石逻辑**
- `GetEnergyGatherInfo()` - 获取能量汇聚信息
- `GetBarrierLevel(UserInfo user)` - 获取神圣结界等级
- `GetUpgradeExp(int currentLevel)` - 获取升级所需经验
- `LoadExpConfig()` - 加载经验配置
- `ReloadConfig()` - 重新加载配置

#### 3. BreakthroughConfig.cs 配置管理
- `BreakthroughConfigManager.GetConfig(int level)` - 获取指定等级配置
- `BreakthroughConfigManager.GetAllConfigs()` - 获取所有配置
- `BreakthroughConfigManager.GetMaxLevel()` - 获取最大等级

---

### 📊 完整调用流程图

```
前端界面点击
    ↓
JavaScript函数
    ↓
window.external接口
    ↓
Form1.cs接口方法
    ↓
业务逻辑静态类
    ↓
DataProcess数据处理
    ↓
数据库/文件操作
```

### 🎯 关键函数调用示例

#### 成长突破完整流程：
1. 前端：`executeBreakthrough()` 
2. 接口：`window.external.GrowthBreakthrough_TryBreakthrough(selectedPhoenixStones)`
3. Form1：`GrowthBreakthrough_TryBreakthrough(int phoenixStoneCount)`
4. 核心：`GrowthBreakthrough.TryBreakthrough(null, phoenixStoneCount)`
5. 数据：`DataProcess` 相关方法处理宠物数据和道具消耗

#### 能量汇聚完整流程：
1. 前端：`useJulingStone()`
2. 接口：`window.external.EnergyGather_UseJulingStone(count)`
3. Form1：`EnergyGather_UseJulingStone(string count)`
4. 核心：`EnergyGather.UseJulingStone(useCount)`
5. 数据：`DataProcess` 相关方法处理用户数据和道具消耗

这个功能涉及了**前端UI交互 → JavaScript逻辑 → COM接口调用 → C#业务逻辑 → 数据持久化**的完整技术栈。