﻿namespace Shikong.Pokemon2.PCG
{
    public class CompressJson
    {
        private readonly string[] _压缩1 = { "\"},{\"", "fg" };
        private readonly string[] _宠物序号 = { "宠物序号", "id" };
        private readonly string[] _形象 = { "形象", "xx" };
        private readonly string[] _等级 = { "等级", "dj" };
        private readonly string[] _当前经验 = { "当前经验", "jy" };
        private readonly string[] _五行 = { "五行", "wx" };
        private readonly string[] _生命 = { "生命", "sm" };
        private readonly string[] _魔法 = { "魔法", "mf" };
        private readonly string[] _最大生命 = { "最大生命", "sm1" };
        private readonly string[] _最大魔法 = { "最大魔法", "mf1" };
        private readonly string[] _攻击 = { "攻击", "gj" };
        private readonly string[] _防御 = { "防御", "fy" };
        private readonly string[] _闪避 = { "闪避", "sb" };
        private readonly string[] _速度 = { "速度", "sd" };
        private readonly string[] _状态 = { "状态", "zt" };
        private readonly string[] _宠物名字 = { "宠物名字", "mz" };
        private readonly string[] _自定义宠物名字 = { "自定义宠物名字", "mz1" };
        private readonly string[] _位置 = { "位置", "wz" };
        private readonly string[] _成长 = { "成长", "cz" };
        private readonly string[] _命中 = { "命中", "mz1" };
        private readonly string[] _境界 = { "境界", "jj"};
        private readonly string[] _成长上限 = { "成长上限", "czsx" };
        private readonly string[] _成长突破等级 = { "成长突破等级", "cztpdj" };
        private readonly string[] _成长突破累计成功率 = { "成长突破累计成功率", "cztplj" };
        private readonly string[] _种族突破等级 = { "种族突破等级", "zztpdj" };
        private readonly string[] _种族突破累计成功率 = { "种族突破累计成功率", "zztplj" };
        private readonly string[] _压缩2 = { "\":\"", "'" };
        public string UncompressPetJson(string json)
        {

            json = json.Replace(_压缩1[1], _压缩1[0]);
            json = json.Replace(_压缩2[1], _压缩2[0]);
            json = json.Replace(_宠物序号[1], _宠物序号[0]);
            json = json.Replace(_形象[1], _形象[0]);
            json = json.Replace(_等级[1], _等级[0]);
            json = json.Replace(_五行[1], _五行[0]);
            json = json.Replace(_最大生命[1], _最大生命[0]);
            json = json.Replace(_最大魔法[1], _最大魔法[0]);
            json = json.Replace(_生命[1], _生命[0]);
            json = json.Replace(_魔法[1], _魔法[0]);
            json = json.Replace(_命中[1], _命中[0]);
            json = json.Replace(_攻击[1], _攻击[0]);
            json = json.Replace(_防御[1], _防御[0]);
            json = json.Replace(_闪避[1], _闪避[0]);
            json = json.Replace(_速度[1], _速度[0]);
            json = json.Replace(_状态[1], _状态[0]);
            json = json.Replace(_宠物名字[1], _宠物名字[0]);
            json = json.Replace(_自定义宠物名字[1], _自定义宠物名字[0]);
            json = json.Replace(_境界[1], _境界[0]);
            json = json.Replace(_位置[1], _位置[0]);
            json = json.Replace(_成长[1], _成长[0]);
            json = json.Replace(_当前经验[1], _当前经验[0]);
            json = json.Replace(_成长上限[1], _成长上限[0]);
            json = json.Replace(_成长突破等级[1], _成长突破等级[0]);
            json = json.Replace(_成长突破累计成功率[1], _成长突破累计成功率[0]);
            json = json.Replace(_种族突破等级[1], _种族突破等级[0]);
            json = json.Replace(_种族突破累计成功率[1], _种族突破累计成功率[0]);

            return json;
        }
        private readonly string[] _压缩11 = { "\"},{\"", "fg" };
        private readonly string[] _压缩21 = { "\":\"", "'" };
        private readonly string[] _道具类型id = { "道具类型ID", "ID_" };
        private readonly string[] _道具序号 = { "道具序号", "xh" };
        private readonly string[] _道具位置 = { "道具位置", "wz" };
        private readonly string[] _道具数量 = { "道具数量", "sl" };
        internal string CompressPropJson(string json)
        {
            json = json.Replace(_道具类型id[0], _道具类型id[1]);
            json = json.Replace(_道具序号[0], _道具序号[1]);
            json = json.Replace(_道具位置[0], _道具位置[1]);
            json = json.Replace(_道具数量[0], _道具数量[1]);
            json = json.Replace(_压缩11[0], _压缩11[1]);
            json = json.Replace(_压缩21[0], _压缩21[1]);
            return json;
        }
        public string UncompressPropJson(string json)
        {
            json = json.Replace(_道具类型id[1], _道具类型id[0]);
            json = json.Replace(_道具序号[1], _道具序号[0]);
            json = json.Replace(_道具位置[1], _道具位置[0]);
            json = json.Replace(_道具数量[1], _道具数量[0]);
            json = json.Replace(_压缩11[1], _压缩11[0]);
            json = json.Replace(_压缩21[1], _压缩21[0]);
            return json;
        }
    }
}
