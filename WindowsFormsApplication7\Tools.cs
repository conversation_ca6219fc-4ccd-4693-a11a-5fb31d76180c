﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;

namespace Shikong.Pokemon2.PCG
{
    internal class Tools
    {   
        internal static void ForcedExit(string reason)
        {   
            LogSystem.JoinLog(LogSystem.EventKind.强制退出,reason);
            try
            {
                LogSystem.SaveLog();
            }
            catch
            {
                Environment.Exit(0);
            }       
            Environment.Exit(0);
        }

        internal class GetTime
        {   
            
            private static Dictionary<int, string> _ntpServerDict = new Dictionary<int, string>
            {
                {1, "cn.pool.ntp.org"},
                {2, "time.pool.aliyun.com"},
                {3, "time.windows.com"},
                {4, "time.nist.gov"},
                {5, "s2d.time.edu.cn"},
                {6, "s2e.time.edu.cn"},
                {7, "s2f.time.edu.cn"},
                {8, "s2g.time.edu.cn"}
            };

            
            internal static int GetNtp()
            {   
                
                short xh = Convert.ToInt16(new DataProcess().ReadFile(@"PageMain/NtpCfg.ini", true));

                string ntpServer = _ntpServerDict[xh];

                try
                {
                    var time = new ConvertJson().GetWeb("http://*************/getTime.php");
                    return Convert.ToInt32(Convert.ToDateTime(time).ToString("yyyyMMdd"));
                }
                catch
                {
                    return -1;
                }
                
                //try
                //{
                //    var ntpData = new byte[48];
                //    ntpData[0] = 0x1B;
                //    var addresses = Dns.GetHostEntry(ntpServer).AddressList;
                //    var ipEndPoint = new IPEndPoint(addresses[0], 123);
                //    var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
                //    socket.Connect(ipEndPoint);

                //    //如果NTP被阻止，停止代码挂起
                //    socket.ReceiveTimeout = 20000;
                //    socket.Send(ntpData);
                //    socket.Receive(ntpData);
                //    socket.Close();
                //    const byte serverReplyTime = 40;
                //    ulong intPart = BitConverter.ToUInt32(ntpData, serverReplyTime);
                //    ulong fractPart = BitConverter.ToUInt32(ntpData, serverReplyTime + 4);
                //    intPart = SwapEndianness(intPart);
                //    fractPart = SwapEndianness(fractPart);
                //    var milliseconds = (intPart * 1000) + ((fractPart * 1000) / 0x100000000L);
                //    var utcTime =
                //        new DateTime(1900, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds((long) milliseconds);
                //    TimeZoneInfo chinaZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                //    var networkDateTime =
                //        Convert.ToInt32(TimeZoneInfo.ConvertTimeFromUtc(utcTime, chinaZone).ToString("yyyyMMdd"));

                //    //Console.WriteLine(networkDateTime);
                //    return networkDateTime;
                //}
                //catch
                //{
                //    return -1;
                //}
            }

            private static uint SwapEndianness(ulong x)
            {
                return (uint) (((x & 0x000000ff) << 24) + ((x & 0x0000ff00) << 8) + ((x & 0x00ff0000) >> 8) +
                               ((x & 0xff000000) >> 24));
            }

            internal string GetSystemTime()
            {
                DateTime d1 = DateTime.Now.ToLocalTime();
                return d1.ToString("yyyyMMddHHmm");
            }

            internal double GetSystemTS() //取系统时间戳
            {
                DateTime d1 = DateTime.Now.ToLocalTime();
                DateTime d2 = new DateTime(2010, 1, 1);
                double d = d1.Subtract(d2).TotalMilliseconds;
                return d;
            }
        }
    }
}
