﻿using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;

namespace Shikong.Pokemon2.PCG
{
    public partial class Ruler : Form
    {
        private static bool _read;

        public Ruler()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (File.Exists(DataProcess.PF_Path))
            {
                UserInfo 用户 = new DataProcess().ReadUserInfo();
                用户.条款 = "Agree";
                new DataProcess().SaveUserDataFile(用户);
            }

            Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            Process.Start("http://47.57.153.101/forum.php?mod=viewthread&tid=2109&page=1&extra=#pid496678");
            _read = true;
            if (checkBox1.Checked && _read)
            {
                button1.Enabled = true;
            }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            Tools.ForcedExit("不同意用户条款");
        }

        private void 用户条款_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (!_read || !checkBox1.Checked)
            {
                Tools.ForcedExit("不同意用户条款");
            }
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                _read = true;
            }
        }

        private void Ruler_Load(object sender, EventArgs e)
        {

        }
    }
}
