﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Shikong.Pokemon2.PCG
{
    public partial class setMonsterJson : Form
    {
        public setMonsterJson()
        {
            InitializeComponent();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            DataProcess.GameForm.webBrowser1.Document.InvokeScript("setMap", new object[] { "test" });
            DataProcess.GameForm.updateBattle_page("test", "0");
        }

        private void setMonsterJson_Load(object sender, EventArgs e)
        {
            textBox1.Text = ConvertJsonString(File.ReadAllText("testMonster.txt"));
        }
        private string ConvertJsonString(string str)
        {


            //格式化json字符串
            JsonSerializer serializer = new JsonSerializer();
            TextReader tr = new StringReader(str);
            JsonTextReader jtr = new JsonTextReader(tr);
            object obj = serializer.Deserialize(jtr);
            if (obj != null)
            {
                StringWriter textWriter = new StringWriter();
                JsonTextWriter jsonWriter = new JsonTextWriter(textWriter)
                {
                    Formatting = Formatting.Indented,
                    Indentation = 4,
                    IndentChar = ' '
                };
                serializer.Serialize(jsonWriter, obj);
                return textWriter.ToString();
            }
            else
            {
                return str;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            File.WriteAllText("testMonster.txt", textBox1.Text);
        }

        private void button3_Click(object sender, EventArgs e)
        {
            PetInfo pet = new PetInfo();
            pet.指定五行 = textBox3.Text;
            pet.成长 = textBox2.Text;
            pet.当前经验 = textBox4.Text;
            var info = PetCalc.CalcPetAttribute(pet, false, true);
            MonsterInfo monster = new MonsterInfo();
            monster.怪物名字 = "测试怪物";
            monster.怪物五行 = pet.五行;
            monster.序号 = "1";
            monster.固定属性 = new PetInfo();
            monster.固定属性.攻击 = info.攻击;
            monster.固定属性.生命 = info.最大生命;
            monster.固定属性.魔法 = info.最大魔法;
            monster.固定属性.最大生命 = info.最大生命;
            monster.固定属性.最大魔法 = info.最大魔法;
            monster.固定属性.生命 = info.生命;
            monster.固定属性.速度 = info.速度;
            monster.固定属性.闪避 = info.闪避;
            monster.固定属性.防御 = info.防御;
            monster.固定属性.魔法 = info.魔法;
            monster.固定属性.五行 = null;
            monster.固定属性.宠物名字 = null;
            List<MonsterInfo> mi = new List<MonsterInfo>();
            mi.Add(monster);

            var jSetting = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore };
            string[] props = { "s", "抵消加深", "格挡概率", "自回血", "佩戴宠物_", "地狱之门", "抵消", "加深", "吸魔", "吸血", "宠物名字", "位置", "信息", "等级", "技能显示", "五行", "_信息", "_技能显示", "减防", "减攻", "减血", "减速", "减命中", "涅槃加成", "暴击回合", "状态" }; //排除掉，不能让前端看到的字段。为true的话就是只保留这些字段
            jSetting.ContractResolver = new LimitPropsContractResolver(props, false);
       
            textBox1.Text = textBox1.Text = ConvertJsonString(JsonConvert.SerializeObject(mi, jSetting));
        }
    }
}
