﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="误报提示" xml:space="preserve">
    <value>如有误报，请勿再开游戏，立即联系开发组进行处理！</value>
  </data>
  <data name="存档版本过低" xml:space="preserve">
    <value>存档版本过低，请联系管理员进行处理！</value>
  </data>
  <data name="无法获取活动地图" xml:space="preserve">
    <value>无法获取活动地图！</value>
  </data>
  <data name="网络有问题正在重试" xml:space="preserve">
    <value>网络有问题，正在重试！</value>
  </data>
  <data name="测试存档" xml:space="preserve">
    <value>该存档为测试存档,无法继续游戏！</value>
  </data>
  <data name="游戏版本过低" xml:space="preserve">
    <value>检测到新版本，请及时更新！</value>
  </data>
  <data name="警告" xml:space="preserve">
    <value>警告</value>
  </data>
  <data name="每日礼包领取成功" xml:space="preserve">
    <value>领取每日礼包成功，祝您游戏愉快！</value>
  </data>
  <data name="多次作弊警告" xml:space="preserve">
    <value>出现错误！错误代码：0x0000D1F</value>
  </data>
  <data name="重复注册提示" xml:space="preserve">
    <value>检测到您已创立了存档，请勿重复注册！</value>
  </data>
  <data name="反虚拟机" xml:space="preserve">
    <value>本游戏无法在虚拟机中运行！</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>时空单机指令列表：&lt;/br&gt;输入"/改名 要改的名字"改名；&lt;/br&gt;输入“/备份”以打开备份存档目录；&lt;/br&gt;输入"/更新日志"查看更新日志；&lt;/br&gt;输入"/NOBOSS"可避免刷怪数达到要求后进入必遇BOSS模式（游戏重启失效）；&lt;/br&gt;输入"/掉落查询"可进入地图掉落查询模式；&lt;/br&gt;输入"/换角色 数字1到6"更换角色；&lt;/br&gt;输入"/Auto?"可查询自动地狱通天指令；&lt;/br&gt;输入"/VIP"可查询当前VIP等级所享受的福利；&lt;/br&gt;输入"/宠物图鉴"可观看宠物图鉴。&lt;/br&gt;输入"/AutoFB?"查看自动副本帮助。&lt;/br&gt;输入"/AutoFB"开始自动副本。&lt;/br&gt;输入"/StopFB"停止一切自动功能&lt;/br&gt;如有更多问题，可在交流群内寻求帮助。</value>
  </data>
  <data name="作弊代码" xml:space="preserve">
    <value>作弊代码：</value>
  </data>
  <data name="严正警告" xml:space="preserve">
    <value>严正警告</value>
  </data>
  <data name="勿回老版" xml:space="preserve">
    <value>请不要试图回到老版本哦！</value>
  </data>
  <data name="改配置警告" xml:space="preserve">
    <value>请勿篡改游戏基本配置！</value>
  </data>
  <data name="游戏已运行" xml:space="preserve">
    <value>游戏已运行！</value>
  </data>
  <data name="新版本提醒" xml:space="preserve">
    <value>检测到游戏有新版本，您是否要立刻更新？</value>
  </data>
  <data name="命名规则提示" xml:space="preserve">
    <value>名字长度不能超过20个半角字符、不能为空且不能有特殊字符！宠物名字不能为0！</value>
  </data>
  <data name="感谢支持" xml:space="preserve">
    <value>感谢您对游戏开发组的大力支持，奖励已发放！</value>
  </data>
  <data name="充值公告" xml:space="preserve">
    <value>2秒钟后，将打开充值页面，请仔细检查所打开页面是否为时空单机的充值页面。账号名为论坛账号，如充错或长时间不到账请联系管理员。感谢您的支持与厚爱！Tips:扫描启动器界面的支付宝红包，充值时使用支付宝可抵扣相应金额！</value>
  </data>
  <data name="Naive" xml:space="preserve">
    <value>Cheating?Naive!</value>
  </data>
  <data name="Naive1" xml:space="preserve">
    <value>请尝试减少一次购买的数量，单次购买使用的货币过多，系统无法运算。</value>
  </data>
  <data name="变速齿轮提示" xml:space="preserve">
    <value>请勿使用变速齿轮等加速软件或篡改系统时间！</value>
  </data>
  <data name="重置确认" xml:space="preserve">
    <value>该副本还未打完，确定要重置该副本吗？</value>
  </data>
  <data name="每日礼包" xml:space="preserve">
    <value>每日礼包</value>
  </data>
  <data name="提示" xml:space="preserve">
    <value>提示</value>
  </data>
  <data name="禁止丢弃" xml:space="preserve">
    <value>称号类道具无法丢弃！</value>
  </data>
  <data name="登录成功" xml:space="preserve">
    <value>登录成功！</value>
  </data>
  <data name="阅读条款" xml:space="preserve">
    <value>点击阅读《时空游戏用户条款》</value>
  </data>
  <data name="修改器警告" xml:space="preserve">
    <value>出现错误！错误代码：0x000001F</value>
  </data>
  <data name="退出" xml:space="preserve">
    <value>退出游戏</value>
  </data>
  <data name="每日礼包领取失败" xml:space="preserve">
    <value>获取网络时间失败,无法领取每日礼包（若今日已领过，请无视）！</value>
  </data>
  <data name="连接错误" xml:space="preserve">
    <value />
  </data>
  <data name="存档损坏提示" xml:space="preserve">
    <value>系统检测到您的存档已损坏，已为您打开备份目录，请尝试挨个恢复离现在时间最近的备份，直至不提示损坏为止！</value>
  </data>
  <data name="聊天异常" xml:space="preserve">
    <value>聊天连接出现异常。</value>
  </data>
  <data name="退出确认" xml:space="preserve">
    <value>您确定要退出游戏吗？</value>
  </data>
  <data name="私自换存档" xml:space="preserve">
    <value>请勿私下交易或交换存档！如果您确实在多个电脑上游戏，请运行tools目录下的信息收集工具，将结果和存档交给管理员处理！</value>
  </data>
  <data name="任务助手" xml:space="preserve">
    <value>任务助手</value>
  </data>
  <data name="每日礼包已领取" xml:space="preserve">
    <value>您今天已经领取过每日礼包了！</value>
  </data>
  <data name="换角色成功" xml:space="preserve">
    <value>更换角色成功，游戏将为您自动刷新页面！</value>
  </data>
  <data name="AutoHelp" xml:space="preserve">
    <value>自动地狱指令为：“/AutoHell 大层数（1至3000） 小层数（1至10）”&lt;/br&gt;自动通天指令为：“/AutoTT 层数（1至500）”。</value>
  </data>
  <data name="变脸提示" xml:space="preserve">
    <value>请注意：变脸是将副宠cc按一定比例转移到主宠而非叠加，且只有添加百炼金丹后才能转移非天赋技能，您确定要这样做吗？</value>
  </data>
  <data name="进程被破坏" xml:space="preserve">
    <value>检测到游戏进程被破坏，请尝试将启动器和主程序加入到杀毒软件信任区！</value>
  </data>
  <data name="没装QQ" xml:space="preserve">
    <value>您的电脑上未安装QQ或QQ版本过低，无法与客服交流！</value>
  </data>
  <data name="按键提示" xml:space="preserve">
    <value>系统监测到您使用了按键精灵，开发组对玩家使用按键精灵持开放态度，不作任何限制。但是，玩家需自行承担使用按键精灵可能带来的作弊误报风险和误操作损失。您确定还要继续使用按键精灵吗？</value>
  </data>
  <data name="获取ip失败" xml:space="preserve">
    <value>获取云服务器ip失败，请重试！</value>
  </data>
  <data name="注册成功" xml:space="preserve">
    <value>注册成功，游戏将在5秒后自动关闭以配置信息。您重启游戏后即可开始游戏！</value>
  </data>
  <data name="选择模式" xml:space="preserve">
    <value>系统检测到您的存档绑定了云服务器，请问您现在在云服务器进行游戏吗？</value>
  </data>
  <data name="转移存档" xml:space="preserve">
    <value>请问您是否需要游戏帮您自动转移老版本存档至该版本？注意：新版本不能覆盖解压在老版本上，否则转移会失败。</value>
  </data>
  <data name="转移成功" xml:space="preserve">
    <value>转移存档成功，系统将在5秒后自动关闭以配置信息。您重启游戏即可开始游戏！</value>
  </data>
  <data name="覆盖解压" xml:space="preserve">
    <value>请勿把新版本覆盖解压到老版本上！</value>
  </data>
  <data name="忘记密码" xml:space="preserve">
    <value>请用您论坛论坛账号所绑定的邮箱发送“重置密码 新密码”至******************，我们会在一个工作日内回复您。</value>
  </data>
  <data name="主程序有更新" xml:space="preserve">
    <value>检测到有新补丁，请及时更新！</value>
  </data>
  <data name="禁止进入该版本" xml:space="preserve">
    <value>你已被禁止进入该版本</value>
  </data>
</root>